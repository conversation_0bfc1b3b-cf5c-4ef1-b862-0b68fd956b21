package com.wunding.learn.recruiting.service.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.wunding.learn.common.viewlimit.model.BaseViewLimit;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 协办人员下发范围表
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("recruiting_assistant_view_limit")
@Schema(name = "RecruitingAssistantViewLimit", description = "协办人员下发范围表")
public class RecruitingAssistantViewLimit extends BaseViewLimit implements Serializable {

    private static final long serialVersionUID = 1L;


}
