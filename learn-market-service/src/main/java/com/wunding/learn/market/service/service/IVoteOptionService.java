package com.wunding.learn.market.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.market.service.admin.dto.VoteContentDTO;
import com.wunding.learn.market.service.admin.dto.VoteCourseWareToLibDTO;
import com.wunding.learn.market.service.admin.query.VoteContentQuery;
import com.wunding.learn.market.service.admin.vo.VoteContentVO;
import com.wunding.learn.market.service.admin.vo.VoteCourseWareNotInLibVO;
import com.wunding.learn.market.service.client.dto.VoteOptionClientDTO;
import com.wunding.learn.market.service.client.dto.VoteOptionRankDTO;
import com.wunding.learn.market.service.client.query.VoteOptionClientQuery;
import com.wunding.learn.market.service.model.VoteOption;
import java.util.List;
import org.springframework.scheduling.annotation.Async;

/**
 * <p> 投票选项表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">axr</a>
 * @since 2022-11-02
 */
public interface IVoteOptionService extends IService<VoteOption> {

    /**
     * 添加投票内容
     *
     * @param voteContentDTO dto对象
     * @return 新增数量
     */
    int addVoteContent(VoteContentDTO voteContentDTO);

    /**
     * 编辑投票内容
     *
     * @param voteContentDTO dto对象
     * @return 修改数量
     */
    int updateVoteContent(VoteContentDTO voteContentDTO);

    /**
     * 删除投票内容
     *
     * @param id 数据id
     * @return 删除数量
     */
    int deleteVoteContent(String id);

    /**
     * 查询投票内容列表
     *
     * @param voteContentQuery dto对象
     * @return 投票内容列表
     */
    PageInfo<VoteContentVO> queryVoteContentListByTitle(VoteContentQuery voteContentQuery);

    VoteContentDTO getInfoById(String id);

    /**
     * 导出投票内容管理列表
     */
    @Async
    void exportData(VoteContentQuery query);

    List<VoteCourseWareNotInLibVO> queryNotInLibCoursewareListByVoteOptionId(String voteId);

    void storageVoteContentCourseware(VoteCourseWareToLibDTO voteCourseWareToLibDTO);

    PageInfo<VoteOptionClientDTO> queryVoteOptionList(VoteOptionClientQuery query);

    PageInfo<VoteOptionRankDTO> getVoteOptionRank(VoteOptionClientQuery query);

}
