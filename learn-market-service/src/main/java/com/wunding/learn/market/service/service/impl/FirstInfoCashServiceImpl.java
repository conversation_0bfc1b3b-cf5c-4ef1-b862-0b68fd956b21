package com.wunding.learn.market.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.common.constant.other.ClientTypeEnum;
import com.wunding.learn.common.constant.redis.CacheRedisDbConst;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.market.FirstInfoContentEnum;
import com.wunding.learn.common.redis.annotaion.RedisCacheable;
import com.wunding.learn.common.util.redis.RedisDBUtil;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.market.service.client.dto.FirstInfoHomeDTO;
import com.wunding.learn.market.service.client.dto.FirstInfoNoticeDTO;
import com.wunding.learn.market.service.mapper.FirstInfoMapper;
import com.wunding.learn.market.service.model.FirstInfo;
import com.wunding.learn.market.service.service.IFirstInfoCashService;
import com.wunding.learn.project.api.service.ProjectFeign;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Service("firstInfoCashService")
@Slf4j
public class FirstInfoCashServiceImpl extends ServiceImpl<FirstInfoMapper, FirstInfo> implements IFirstInfoCashService {

    @Resource
    private ProjectFeign projectFeign;
    @Resource
    private FileFeign fileFeign;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public FirstInfoHomeDTO getFirstInfoCacheById(String id) {
        String ket_FirstInfo = "getFirstInfoCacheById:";
        String currentOs = UserThreadContext.getOs();
        String currentUserId = UserThreadContext.getUserId();
        String firstInfoKey = RedisDBUtil.getFullKey(CacheRedisDbConst.CACHE, ket_FirstInfo,
            id + ":" + currentOs);
        FirstInfoHomeDTO homeDTO = (FirstInfoHomeDTO) redisTemplate.opsForValue().get(firstInfoKey);
        if (Optional.ofNullable(homeDTO).isPresent()) {
            return homeDTO;
        }
        FirstInfoHomeDTO firstInfoCacheById = baseMapper.getFirstInfoCacheById(id);

        Map<String, String> pcImgMap = fileFeign.getImageUrlsByIds(Collections.singletonList(id),
            ImageBizType.FIRST_IMAGE.toString());
        Map<String, String> appImgMap = fileFeign.getImageUrlsByIds(Collections.singletonList(id),
            ImageBizType.FIRST_IMAGE_APP.toString());

        if (ClientTypeEnum.CLIECNT_TYPE_PC.getName().equals(currentOs)) {
            Optional.ofNullable(pcImgMap.get(id)).ifPresent(firstInfoCacheById::setImage);
        } else {
            Optional.ofNullable(appImgMap.get(id)).ifPresent(firstInfoCacheById::setImage);
        }
        if (FirstInfoContentEnum.info.name().equals(firstInfoCacheById.getCategoryType())) {
            firstInfoCacheById.setUrl(
                fileFeign.getFileUrl(firstInfoCacheById.getCategoryId(), FileBizType.InfoWareFile.name()));
        }
        // 学习项目类型通知新增是否直接跳转入任务状态输出
        if (FirstInfoContentEnum.project.name().equals(firstInfoCacheById.getCategoryType())) {
            firstInfoCacheById.setIsOperation(
                projectFeign.verifyIsJoinProject(firstInfoCacheById.getCategoryId(), currentUserId));
        }
        redisTemplate.opsForValue().set(firstInfoKey, firstInfoCacheById, 300, TimeUnit.SECONDS);
        return firstInfoCacheById;
    }

    @Override
    public void removeFirstInfoCache(String id) {
        if (StringUtils.isBlank(id)) {
            return;
        }
        String ket_FirstInfo = "getFirstInfoCacheById:";
        String ket_NoticeInfo = "getNoticeInfoCacheById:";
        List<String> keys = new ArrayList<>();
        if (id.contains(",")) {
            String[] split = id.split(",");
            for (String s : split) {
                String firstInfoPCKey = RedisDBUtil.getFullKey(CacheRedisDbConst.CACHE, ket_FirstInfo, s + ":PC");
                String firstInfoAPPKey = RedisDBUtil.getFullKey(CacheRedisDbConst.CACHE, ket_FirstInfo, s + ":APP");
                String noticeInfoKey = RedisDBUtil.getFullKey(CacheRedisDbConst.CACHE, ket_NoticeInfo, s);
                keys.add(firstInfoPCKey);
                keys.add(firstInfoAPPKey);
                keys.add(noticeInfoKey);
            }
        } else {
            String firstInfoPCKey = RedisDBUtil.getFullKey(CacheRedisDbConst.CACHE, ket_FirstInfo, id + ":PC");
            String firstInfoAPPKey = RedisDBUtil.getFullKey(CacheRedisDbConst.CACHE, ket_FirstInfo, id + ":APP");
            String noticeInfoKey = RedisDBUtil.getFullKey(CacheRedisDbConst.CACHE, ket_NoticeInfo, id);
            keys.add(firstInfoPCKey);
            keys.add(firstInfoAPPKey);
            keys.add(noticeInfoKey);
        }
        redisTemplate.delete(keys);
    }

    @Override
    @RedisCacheable(cacheName = "getNoticeInfoCacheById")
    public FirstInfoNoticeDTO getNoticeInfoCacheById(String id) {
        String userId = UserThreadContext.getUserId();
        FirstInfoNoticeDTO dto = baseMapper.getNoticeInfoCacheById(id);
        Map<String, String> imgMap = fileFeign.getImageUrlsByIds(Collections.singletonList(id),
            ImageBizType.NOTICE_IMAGE.toString());
        Optional.ofNullable(imgMap.get(dto.getId())).ifPresent(dto::setImage);
        // 学习项目类型通知新增是否直接跳转入任务状态输出
        if (FirstInfoContentEnum.project.name().equals(dto.getCategoryType())) {
            dto.setIsOperation(projectFeign.verifyIsJoinProject(dto.getCategoryId(), userId));
        }
        return dto;
    }
    
    @Override
    public void removeFirstInfoCacheByResourceId(String resourceId) {
        if (StringUtils.isBlank(resourceId)) {
            return;
        }
        // 通过资源id查询头条
        String ids = list(
            new LambdaQueryWrapper<FirstInfo>().select(FirstInfo::getId).eq(FirstInfo::getCategoryId, resourceId))
            .stream().map(FirstInfo::getId).collect(
                Collectors.joining(","));
        log.info("-----ids----> {}", ids);
        removeFirstInfoCache(ids);
    }
}
