<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.evaluation.service.mapper.EvaluationMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.evaluation.service.mapper.EvaluationMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.evaluation.service.model.Evaluation">
        <!--@Table evaluation-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="eval_name" jdbcType="VARCHAR"
          property="evalName"/>
        <result column="memo" jdbcType="VARCHAR"
          property="memo"/>
        <result column="start_time" jdbcType="TIMESTAMP"
          property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP"
          property="endTime"/>
        <result column="org_id" jdbcType="VARCHAR"
          property="orgId"/>
        <result column="is_publish" jdbcType="TINYINT"
          property="isPublish"/>
        <result column="publish_by" jdbcType="VARCHAR"
          property="publishBy"/>
        <result column="publish_time" jdbcType="TIMESTAMP"
          property="publishTime"/>
        <result column="is_available" jdbcType="TINYINT"
          property="isAvailable"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
        <result column="is_train" jdbcType="TINYINT"
          property="isTrain"/>
        <result column="templet_id" jdbcType="VARCHAR"
          property="templetId"/>
        <result column="source_type" jdbcType="TINYINT"
          property="sourceType"/>
        <result column="evaluation_type" jdbcType="VARCHAR"
          property="evaluationType"/>
        <result column="evaluation_object" jdbcType="VARCHAR"
          property="evaluationObject"/>
        <result column="phase_id" jdbcType="VARCHAR"
          property="phaseId"/>
        <result column="is_evaluation_results" jdbcType="TINYINT"
          property="isEvaluationResults"/>
        <result column="source_type" jdbcType="TINYINT"
          property="sourceType"/>
        <result column="view_limit_type" jdbcType="TINYINT"
          property="viewLimitType"/>
        <result column="project_id" jdbcType="VARCHAR"
          property="projectId"/>
        <result column="schedule_id" jdbcType="VARCHAR"
          property="scheduleId"/>
        <result column="score_type" jdbcType="DOUBLE"
          property="scoreType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
             , eval_name
             , memo
             , start_time
             , end_time
             , org_id
             , is_publish
             , publish_by
             , publish_time
             , is_available
             , is_del
             , create_by
             , create_time
             , update_by
             , update_time
             , is_train
             , templet_id
             , source_type
             , evaluation_type
             , evaluation_object
             , phase_id
             , is_evaluation_results
             , source_type
             , view_limit_type
             , project_id
             , score_type
    </sql>
    <sql id="Base_Column_List_With_Alia_A">
        a.id
        , a.eval_name
             , a.memo
             , a.start_time
             , a.end_time
             , a.org_id
             , a.is_publish
             , a.publish_by
             , a.publish_time
             , a.is_available
             , a.is_del
             , a.create_by
             , a.create_time
             , a.update_by
             , a.update_time
             , a.is_train
             , a.templet_id
             , a.evaluation_type
             , a.evaluation_object
             , a.phase_id
             , a.is_evaluation_results
             , a.source_type
             , a.view_limit_type
             , a.project_id
             , a.score_type
    </sql>

    <select id="listDetailByProjectIds" parameterType="com.wunding.learn.evaluation.api.query.EvaluationProjectQuery"
      resultType="com.wunding.learn.evaluation.api.dto.EvaluationListDTO" useCache="false">
        select
        e.id,
        e.eval_name,
        e.start_time,
        e.end_time,
        e.create_by,
        e.create_time,
        e.evaluation_type,
        e.evaluation_object,
        e.publish_by,
        e.publish_time,
        e.is_publish,
        e.score_type
        from evaluation e
        inner join sys_org g on g.id = e.org_id
        where e.is_del = 0
        and (
            (
            <if test="params.projectIds != null and params.projectIds.size() > 0">
                e.evaluation_object in
                <foreach collection="params.projectIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            and e.evaluation_type = 1
            )
        <if test="params.examinationIds != null and params.examinationIds.size() > 0">
            or
            (e.evaluation_type = 2 and e.evaluation_object in
            <foreach collection="params.examinationIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        )
        <if test="params.managerAreaOrgIds != null and params.managerAreaOrgIds.size() > 0">
            <foreach collection="params.managerAreaOrgIds" item="item" open="and (" separator="or">
                g.level_path like concat(#{item},'%')
            </foreach>
            or (e.org_id = #{params.currentOrgId} and e.create_by = #{params.currentUserId}))
        </if>
        order by e.is_publish asc,e.publish_Time desc limit 1
    </select>

    <select id="listDetail" parameterType="com.wunding.learn.evaluation.api.query.EvaluationProjectQuery"
      resultType="com.wunding.learn.evaluation.api.dto.EvaluationListDTO" useCache="false">
        select
        e.id,
        e.eval_name,
        e.start_time,
        e.end_time,
        e.create_by,
        e.create_time,
        e.evaluation_type,
        e.evaluation_object,
        e.publish_by,
        e.publish_time,
        e.is_publish,
        e.score_type,
        e.phase_id,
        e.schedule_id
        from evaluation e
                inner join sys_org g on g.id = e.org_id
        where e.is_del = 0
        and ((e.evaluation_object = #{params.projectId} and e.evaluation_type = 1)
        <if test="params.examinationIds != null and params.examinationIds.size() > 0">
            or
             (e.evaluation_type = 2 and e.evaluation_object in
            <foreach collection="params.examinationIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>)
        <if test="params.managerAreaOrgIds != null and params.managerAreaOrgIds.size() > 0">
            <foreach collection="params.managerAreaOrgIds" item="item" open="and (" separator="or">
                g.level_path like concat(#{item},'%')
            </foreach>
            or (e.org_id = #{params.currentOrgId} and e.create_by = #{params.currentUserId}))
        </if>
        order by e.is_publish asc,e.publish_Time desc
    </select>

    <select id="selectProjectEvaluationQuestionAvgScore"
           resultType="com.wunding.learn.evaluation.api.dto.ProjectSimpleEvalOrgDTO">
      select
      er.eval_id evalId,
      er.answer_content questionItemId,
      round(ifnull(avg(er.mark),0),2) itemAvgScore
      from eval_reply er
      where er.mark != 0.0
      and  er.eval_id in
      <foreach collection="evalIdList" open="(" close=")" item="evalId" separator=",">
        #{evalId}
      </foreach>
      group by er.eval_id ,er.answer_content
    </select>


     <select id="selectProjectLecturerEvaluationAvgScore"
          resultType="com.wunding.learn.evaluation.api.dto.ProjectEvalAvgScore">
       select
         e.project_id  projectId,
         round(ifnull(avg(er.mark),0),4) lecturerAvgScore
       from evaluation e
              inner join eval_reply er on e.id = er.eval_id
       where e.is_del = 0
         and e.evaluation_type = 2
         and er.mark != 0.0
         and  e.project_id in
         <foreach collection="projectIdList" open="(" close=")" item="projectId" separator=",">
           #{projectId}
         </foreach>
       group by e.project_id
     </select>


  <select id="selectProjectJoinEvalInfo" parameterType="list"
    resultType="com.wunding.learn.evaluation.api.dto.ProjectJoinEvalDTO">
    select
    e.project_id  projectId,
    round(ifnull(avg(er.mark),0),2) value
    from evaluation e
    inner join eval_reply er on e.id = er.eval_id
    inner join sys_user c on er.answer_by = c.id
    where e.is_del = 0
    and c.is_del = 0
    and e.evaluation_type = 1
    and er.mark != 0.0
    and  e.project_id in
    <foreach collection="list" open="(" close=")" item="query" separator=",">
      #{query.projectId}
      and c.id in
      <foreach collection="query.userIds" open="(" item="userId" separator="," close=")">
        #{userId}
      </foreach>
    </foreach>
    group by e.project_id
  </select>


    <select id="selectProjectEvaluationAvgScore"
      resultType="com.wunding.learn.evaluation.api.dto.ProjectEvalAvgScore">
        select
          e.project_id  projectId,
          round(ifnull(avg(er.mark),0),2) projectAvgScore
        from evaluation e
               inner join eval_reply er on e.id = er.eval_id
        where e.is_del = 0
          and e.evaluation_type = 1
          and er.mark != 0.0
          and  e.project_id in
          <foreach collection="projectIdList" open="(" close=")" item="projectId" separator=",">
            #{projectId}
          </foreach>
        group by e.project_id
    </select>

    <select id="selectProjectEvaluationScoreItemNum" resultType="java.lang.Integer">
      select
         count (eq.id)
      from eval_question  eq
      left join evaluation e  on e.id = eq.eval_id
      where eq.question_type = 6
      and e.evaluation_object #{projectId};
    </select>

    <!--嵌套子查询-待优化-->
    <select id="listLecturerDetail" parameterType="com.wunding.learn.evaluation.api.query.EvaluationProjectQuery"
    resultType="com.wunding.learn.evaluation.api.dto.EvaluationListDTO">
    select
    e.id,
    e.eval_name,
    e.start_time,
    e.end_time,
    e.create_by,
    e.create_time,
    e.evaluation_type,
    e.evaluation_object,
    e.publish_by,
    e.publish_time,
    e.is_publish,
    e.score_type ,
    (select round(avg(mark), 2) from(
    select ifnull(avg(r.mark),0) mark
    from eval_reply r
    where r.eval_id=e.id
    and r.mark != 0.0
    group by r.record_id) b) avgScore
    from evaluation e
    inner join sys_org g on g.id = e.org_id
    where e.is_del = 0
    <if test="params.examinationIds != null and params.examinationIds.size() > 0">
      and
      (e.evaluation_type = 2 and e.evaluation_object in
      <foreach collection="params.examinationIds" open="(" close=")" item="item" separator=",">
        #{item}
      </foreach>
      )
    </if>)
    <if test="params.managerAreaOrgIds != null and params.managerAreaOrgIds.size() > 0">
      <foreach collection="params.managerAreaOrgIds" item="item" open="and (" separator="or">
        g.level_path like concat(#{item},'%')
      </foreach>
      or (e.org_id = #{params.currentOrgId} and e.create_by = #{params.currentUserId}))
    </if>
    order by e.is_publish asc,e.publish_Time desc
  </select>



    <select id="getEvaluationForAnalysis"
      resultType="com.wunding.learn.evaluation.service.admin.dto.EvaluationAnalysisBaseDTO" useCache="false">
        select id,
               eval_name as surveyName,
               memo as description,
               start_time,
               end_time,
               is_publish,
               (select count(1)
                from eval_record er
                where er.eval_id = s.id
                  and exists(select 1 from eval_reply ery where ery.eval_id = s.id and
                    ery.answer_by = er.user_id)) recordCount,
               (select ifnull(sum(it.mark), 0)
                from eval_question_item it
                where it.eval_id = s.id)                                           sumScore,
                round((select ifnull(sum(r.mark), 0)/sum( case ifnull( r.mark , 0) when 0 then 0 else 1 end  ) mark
                from eval_reply r
                where r.eval_id = s.id  ),2) avgScore
        from evaluation s
        where id = #{surveyId}
    </select>

    <select id="evaluationInfo" resultType="com.wunding.learn.evaluation.api.dto.EvaluationInfoDTO">
        select y.id,
               y.eval_name,
               y.memo,
               y.start_time,
               y.end_time,
               y.is_publish,
               y.templet_id sourceId,
               y.source_type isSource,
               y.evaluation_type,
               y.evaluation_object,
               y.phase_id,
               y.schedule_id,
               y.is_evaluation_results,
               y.evaluation_object,
               y.view_limit_type viewLimitType,
               y.create_by,
               y.score_type
        from evaluation y
        where y.id = #{evaluationId}
    </select>

    <select id="getEvalNum" resultType="java.lang.Integer" useCache="false">
        select
        count( 1 )
        from
        evaluation e
        inner join eval_record ep on ep.eval_id = e.id
        where
        e.is_publish = 1
        and e.evaluation_object in
        <foreach collection="evaluationObjectList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getLecturerMinScore" resultType="java.lang.Float" useCache="false">
        select ifnull( min(score), 0 ) from (select
        ifnull( avg( ep.mark ), 0 ) score
        from
        evaluation e
        inner join eval_record er on er.eval_id = e.id
        inner join eval_reply ep on ep.eval_id = e.id
        left join eval_question q on ep.question_id = q.id
        where
        q.question_type = 6 and q.question_category = '讲师' and e.is_publish = 1
        and e.evaluation_object in
        <foreach collection="lecturerExaminationIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by
        ep.eval_id) a
    </select>
    <select id="getLecturerAvgScore" resultType="java.math.BigDecimal" useCache="false">
        select
        ifnull(round((sum(mark) / count(1)),2),0)
        from
        eval_reply e
        left join eval_question q on e.question_id = q.id
        where
        e.eval_id in (
        select e.id
        from evaluation e
        where
        evaluation_object in
        <foreach collection="lecturerExaminationIdSet" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and e.evaluation_type = '2'
        )
        and q.question_type = 6
    </select>

    <select id="selectLecturerEvalList"
      resultType="com.wunding.learn.evaluation.service.admin.dto.LecturerEvaluationListDTO">
        select
        e.evaluation_object,
        e.eval_name,
        e.start_time,
        e.end_time,
        e.is_train,
        e.id
        from evaluation e
        where e.evaluation_type = '2' and e.is_del = 0
        <if test="null != startTime and '' != startTime">
            and start_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="null != endTime and '' != endTime">
            and #{endTime,jdbcType=TIMESTAMP} >= e.end_time
        </if>
        <if test="null != lecturerExaminationIds">
            and e.evaluation_object in
            <foreach collection="lecturerExaminationIds" item="item" separator="," open="(" close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        order by e.create_time desc
    </select>

    <!-- 评估统计分析结果 -->
    <resultMap id="EvalStatisticalAnalysisResultMap" type="com.wunding.learn.evaluation.service.admin.dto.EvalStatisticalAnalysisVO" >
        <id column="user_id" property="userId" jdbcType="VARCHAR" />
        <id column="finish_time" property="finishTime" jdbcType="TIMESTAMP" />
        <collection property="evalQuestionCategoryVoList" ofType="com.wunding.learn.evaluation.service.admin.dto.EvalQuestionCategoryVO" column="category" >
            <id column="question_category" property="category" jdbcType="VARCHAR" />
            <collection property="evalQuestionVoList" ofType="com.wunding.learn.evaluation.service.admin.dto.EvalQuestionVO" column="question_id">
                <id column="question_id" property="questionId" jdbcType="VARCHAR" />
                <result column="question_no" property="questionNo" jdbcType="INTEGER" />
                <result column="question_name" property="questionName" jdbcType="VARCHAR" />
                <result column="question_type" property="questionType" jdbcType="INTEGER" />
                <collection property="evalQuestionItemVoList" ofType="com.wunding.learn.evaluation.service.admin.dto.EvalQuestionItemVO" column="item_id" >
                    <id column="item_id" property="itemId" jdbcType="VARCHAR" />
                    <result column="sort_no" property="sortNo" jdbcType="INTEGER" />
                    <result column="item_name" property="itemName" jdbcType="VARCHAR" />
                    <result column="answer_content" property="answerContent" jdbcType="VARCHAR" />
                </collection>
            </collection>
        </collection>
    </resultMap>

    <select id="getEvalStatisticalAnalysis" resultMap="EvalStatisticalAnalysisResultMap" useCache="false">
        select a.answer_by user_id,
               ec.finish_time,
               eq.question_category,
               eq.id                                          as question_id,
               eq.question_no,
               eq.question_type,
               eq.question_name,
               eqi.id                                         as item_id,
               eqi.sort_no,
               eqi.item_name,
               case
                   when eq.question_type = 4
                       then (select er.answer_content
                             from eval_reply er
                             where er.question_id = eq.id
                               and er.answer_by = a.answer_by)
                   else
                       (select er.mark
                        from eval_reply er
                        where er.question_id = eq.id
                          and er.answer_by = a.answer_by
                          and er.answer_content = eqi.id) end as answer_content
        from (
                 select er.answer_by
                 from eval_reply er
                 where er.eval_id = #{evalId,jdbcType=VARCHAR}
                 group by er.answer_by) a
                 left join eval_record ec on a.answer_by = ec.user_id and ec.eval_id = #{evalId,jdbcType=VARCHAR}
                 left join eval_question eq on ec.eval_id = eq.eval_id
                 left join eval_question_item eqi on eq.id = eqi.question_id
        where 1 = 1
        <if test="userId != null and userId != ''">
            and a.answer_by = #{userId}
        </if>
        order by eq.question_no, eqi.sort_no
    </select>

    <select id="getReplyUserList" resultType="com.wunding.learn.evaluation.service.admin.dto.EvalReplyUser" useCache="false">
        select a.answer_by userId,
        ec.finish_time
        from (
        select er.answer_by
        from eval_reply er
        where er.eval_id = #{evalId,jdbcType=VARCHAR}
        group by er.answer_by) a
        left join eval_record ec on a.answer_by = ec.user_id and ec.eval_id = #{evalId,jdbcType=VARCHAR}
        where 1 = 1
        <if test="userId != null and userId != ''">
            and   a.answer_by in
            <foreach collection="userId.split(',')" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by ec.finish_time
    </select>

    <select id="selectProTaskEvaluationListByTime" parameterType="com.wunding.learn.evaluation.api.query.LearningCalendarTaskFeignQuery"
      resultType="com.wunding.learn.common.dto.LearningCalendarTaskDTO" useCache="false">
        select
        e.id,
        e.id as activityId,
        e.eval_name as title,
        e.start_time startTime,
        e.end_time endTime,
        0 isProjectTask,
        case when e.start_time > now() then 0 when now() > e.end_time then 2 else 1 end as status,
        case when (select ec.id from eval_record ec where ec.eval_id = e.id and user_id = #{userId} and ec.is_finish =
        1) is null then 0 else 2 end as userStatus,
        0 isOperation
        from evaluation e
        where e.id in
        <foreach collection="resourceIds" open="(" item="evalId" separator="," close=")">
            #{evalId}
        </foreach>
    </select>

    <select id="getUserEvalStatusInfo" parameterType="com.wunding.learn.evaluation.api.query.UserEvalStatusInfoQuery"
      resultType="com.wunding.learn.evaluation.api.dto.UserEvalStatusInfoDTO" useCache="false">
        select
        e.phase_Id                             as phaseId,
        e.eval_name                            as taskName,
        e.id                                   as taskContent,
        e.start_time                           as startTime,
        e.end_time                             as endTime,
        case
        when date_format(e.start_time, '%Y-%m-%d %H:%i') > date_format(now(), '%Y-%m-%d %H:%i') then 0
        when date_format(now(), '%Y-%m-%d %H:%i') > date_format(e.end_time, '%Y-%m-%d %H:%i') then 2
        else 1 end                         as status,
        if(ec.id is not null, 2, 0)            as userStatus
        from evaluation e
            inner join w_resource_view_limit f on e.id = f.resource_id
            left join eval_record ec on e.id = ec.eval_id and ec.user_id = #{userId} and ec.is_finish = 1
        where
        e.is_del = 0 and e.is_publish = 1
        and e.id in
        <foreach collection="evalIds" open="(" item="evalId" separator="," close=")">
            #{evalId}
        </foreach>
        <if test="isComplete != null and isComplete == 1">
            and ec.id is not null
        </if>
        <if test="isComplete != null and isComplete == 0">
            and ec.id is null
        </if>
        <if test="isExpired != null">
            <if test="isExpired == 1">
                and now() > e.end_time
            </if>
            <if test="isExpired == 0">
                and e.end_time > now()
            </if>
        </if>
        <if test="stageId != null and stageId != ''">
            and e.phase_Id = #{stageId}
        </if>
        and exists(
            select 1
            from w_view_limit_user g
            where g.view_limit_id = f.view_limit_id
            and g.user_id = #{userId}
        )
    </select>

    <select id="getEvalIdsByCalendar" parameterType="com.wunding.learn.common.dto.LearningCalendarTaskQuery" resultType="string" useCache="false">
        select
            e.id
        from evaluation e
        where e.id not in (
                select  ec.eval_id
                from eval_record ec
                where ec.user_id = #{userId}
            )
            and e.is_del = 0
            and e.is_publish = 1
        <if test="null == whichDay">
            and ((date_format(e.start_time, '%Y-%m-%d') >= date_format(#{startTime},'%Y-%m-%d') and
            date_format(e.start_time, '%Y-%m-%d') &lt;=
            date_format(#{endTime},'%Y-%m-%d')) or
            (date_format(e.end_time, '%Y-%m-%d') >= date_format(#{startTime},'%Y-%m-%d') and date_format(e.end_time,
            '%Y-%m-%d') &lt;= date_format(#{endTime},'%Y-%m-%d')) or
            (date_format(e.start_time, '%Y-%m-%d') &lt;= date_format(#{startTime},'%Y-%m-%d') and
            date_format(e.end_time, '%Y-%m-%d') >= date_format(#{endTime},'%Y-%m-%d')))
        </if>
        <if test="null != whichDay and null == today">
            and #{whichDay} = date_format(e.start_time, '%Y-%m-%d')
        </if>
        <if test="null != whichDay and null != today">
            and #{whichDay} >= date_format(e.start_time, '%Y-%m-%d') and #{whichDay} &lt;= date_format(e.end_time,
            '%Y-%m-%d')
        </if>
    </select>

    <select id="getAvgScore" resultType="java.math.BigDecimal" useCache="false">
        select
            ifnull( avg( score ), 0 )
        from
            (
                select
                    ifnull( avg( temp.mark ), 0 ) as score
                from
                    (
                        select
                            distinct( ep.id ),
                                    ep.mark
                        from
                            evaluation e
                                inner join eval_record er on er.eval_id = e.id
                                inner join eval_reply ep on ep.eval_id = e.id
                                inner join eval_question q on ep.question_id = q.id
                        where
                            q.question_type = 6
                          and q.question_category = '讲师'
                          and e.id = #{evalId}
                    ) temp
            ) a
    </select>

    <select id="calculateLecturerScore" resultType="java.math.BigDecimal" useCache="false">
        select ifnull(round((sum(mark) / count(1)), 2), 0)
        from eval_reply er
                 left join eval_question q on er.question_id = q.id
        where exists(
                select 1
                    from evaluation el
                    where el.evaluation_type = '2'
                    and el.id = er.eval_id
                    and el.evaluation_object in
                    <foreach collection="examinationIdList" open="(" separator="," item="item" close=")">
                        #{item}
                    </foreach>
                )
              and q.question_type = 6
    </select>

    <select id="listEvalAvgScore" parameterType="list" resultType="com.wunding.learn.evaluation.api.dto.EvalAvgScoreDTO" useCache="false">
        select a.eval_id evalId, count(distinct a.id) recordCount,
               case count(distinct a.id) when 0 then null
                                         else truncate(sum(b.mark) / count(distinct a.id), 2) end avgScore
        from eval_record a
                 left join eval_reply b on a.id = b.record_id
        where a.eval_id in
            <foreach collection="list" open="(" item="evalId" separator="," close=")">
                #{evalId}
            </foreach>
        group by a.eval_id;
    </select>

    <select id="selectEvaluationList" parameterType="com.wunding.learn.evaluation.api.query.EvaluationListWithLimitQuery"
        resultType="com.wunding.learn.common.dto.TaskAppResourceDTO">
        select a.id                   as resourceId,
               a.eval_name            as resourceName,
               a.task_id              as taskId,
               case
                   when a.end_time <![CDATA[ < ]]> current_timestamp then 2
                   when a.start_time <![CDATA[ > ]]> current_timestamp then 0
                   else 1 end status,
               if(b.id is null, 0, 1) as isFinish
        from evaluation a
            <if test="isViewLimit != null and isViewLimit == 1">
                inner join w_resource_view_limit c on a.id = c.resource_id and c.resource_type = 'EvaluationViewLimit'
            </if>
            left join eval_record b on a.id = b.eval_id and b.user_id = #{currentUserId} and b.is_finish = 1
        where a.project_id = #{projectId}
        and a.is_del = 0
        <if test="isPublish != null">
            and a.is_publish = #{isPublish}
        </if>
        <if test="isViewLimit != null and isViewLimit == 1">
            and exists(select 1 from w_view_limit_user v where c.view_limit_id = v.view_limit_id and v.user_id = #{currentUserId})
        </if>
    </select>

    <select id="listWithUserLimit" parameterType="com.wunding.learn.evaluation.api.query.EvaluationListWithLimitQuery"
        resultMap="BaseResultMap" useCache="false">
        select
            <include refid="Base_Column_List_With_Alia_A"/>
        from evaluation a
            left join w_resource_view_limit b on a.id = b.resource_id and b.resource_type = 'EvaluationViewLimit'
        where
            a.is_publish = 1
            and a.is_del = 0
          <if test="projectId != null and projectId != ''">
              and a.project_id = #{projectId}
          </if>
          and ( (a.view_limit_type != 0 and exists(select 1
                      from w_view_limit_user v
                      where b.view_limit_id = v.view_limit_id
                        and v.user_id = #{currentUserId})) or a.view_limit_type = 0)
    </select>

    <select id="getProjectAvgScoreByProjectIds" resultType="java.math.BigDecimal" useCache="false">
        select round(ifnull(avg(score), 0), 2) from (
               select ifnull(avg(mark), 0) score, e.eval_id
               from evaluation a
                   inner join eval_reply e on a.id = e.eval_id
                    left join eval_question q on e.question_id = q.id
               where a.evaluation_object in
                <foreach collection="projectIds" open="(" item="projectId" separator="," close=")">
                    #{projectId}
                </foreach>
                and a.is_del = 0
                 and q.question_type = 6
                 and question_category = '项目'
       group by e.eval_id, e.record_id) a
    </select>

    <select id="queryPage" resultType="com.wunding.learn.evaluation.api.dto.EvalDTO">
        select
        e.*
        from
        evaluation e
        inner join eval_reply er on er.eval_id = e.id
        where e.is_del = 0
        <if test="params.startTime != null">
            and e.start_time >= #{params.startTime}
        </if>
        <if test="params.endTime != null">
            and #{params.endTime} >= e.end_time
        </if>
        <if test="params.evaluationType != null">
            and e.evaluation_type = #{params.evaluationType}
        </if>
        <if test="params.evalName != null and params.evalName != ''">
            and instr(e.eval_name,#{params.evalName})
        </if>
        <if test="params.evaluationObjectIds != null and params.evaluationObjectIds.size() > 0">
            and e.evaluation_object in
            <foreach collection="params.evaluationObjectIds" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        group by e.id
    </select>

    <select id="getEvalInfoListByEvalObjects" resultType="com.wunding.learn.evaluation.api.dto.EvalDTO" useCache="false">
        select e.evaluation_object,e.id,e.start_time startTime,e.end_time endTime,
               (select count(1) from eval_record where eval_id = e.id) recordCount,
               (select count(1) from eval_record where eval_id = e.id and is_finish = 1) assessedCount,
               (
                   select round(ifnull(avg(score), 0), 2)
                   from (

                            select ifnull(avg(ep.mark), 0) score
                            from  eval_reply ep
                                      left join eval_question q on ep.question_id = q.id
                            where q.question_type = 6
                              and q.question_category = '讲师'
                              and ep.eval_id = e.id
                            group by ep.eval_id

                        ) a
               ) avgScore

        from
            evaluation e
        where 1=1
          AND is_del =0
          AND e.IS_PUBLISH = 1
          <if test="evalObjectSet != null and evalObjectSet.size() > 0">
            AND e.evaluation_object in
            <foreach collection="evalObjectSet" item="item" separator="," open="(" close=")">
              #{item}
            </foreach>
          </if>
    </select>

    <select id="checkEvaluationManagePermissions" resultType="java.lang.Integer" useCache="false">
        select count( 1 ) from evaluation tp inner join sys_org g on g.id = tp.org_id
        where tp.id = #{id,jdbcType=VARCHAR}
        <if test="userManageAreaOrgId !=null and userManageAreaOrgId.size > 0 ">
            and
            <foreach collection="userManageAreaOrgId" open="(" close=")" item="levelPath" separator="or">
                g.level_path like concat(#{levelPath},'%')
            </foreach>
        </if>
    </select>
    <select id="getEvalInfoByEvalObjects"
        resultType="com.wunding.learn.evaluation.api.dto.EvalDTO" useCache="false" >
        select
            sum(( select count( 1 ) from eval_record where eval_id = e.id and is_finish = 1 )) assessedCount,
            round( ifnull( avg( (
            select
            round( ifnull( avg( score ), 0 ), 2 )
            from
            (
            select
            ifnull( avg( ep.mark ), 0 ) score
            from
            eval_reply ep
            left join eval_question q on ep.question_id = q.id
            where
            q.question_type = 6
            and q.question_category = '讲师'
            and ep.eval_id = e.id
            group by
            ep.eval_id
            ) a
            ) ), 0 ), 2 ) avgScore
        from evaluation e
        where e.is_del = 0
            and e.is_publish = 1
            and e.evaluation_object in
            <foreach collection="collect" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
    </select>
</mapper>
