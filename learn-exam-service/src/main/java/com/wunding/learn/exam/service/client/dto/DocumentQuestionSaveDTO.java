package com.wunding.learn.exam.service.client.dto;

import com.wunding.learn.common.dto.lecturerworkbench.DocumentPostBaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p> 保存资料题目dto对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-01-12
 */
@EqualsAndHashCode(callSuper = true)
@Data
 @Schema(name = "DocumentQuestionSaveDTO", description = "保存资料题目dto对象")
public class DocumentQuestionSaveDTO extends DocumentPostBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private String id;

}
