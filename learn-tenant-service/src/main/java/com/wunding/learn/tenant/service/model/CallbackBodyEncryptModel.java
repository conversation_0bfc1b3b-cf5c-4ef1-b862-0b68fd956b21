package com.wunding.learn.tenant.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 企业微信服务商代开发回调密文解密数据表
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("callback_body_encrypt")
@Schema(name = "CallbackBodyEncrypt对象", description = "企业微信服务商代开发回调密文解密数据表")
public class CallbackBodyEncryptModel implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 第三方应用的SuiteId或者代开发应用模板id
     */
    @Schema(description = "第三方应用的SuiteId或者代开发应用模板id")
    @TableField("suite_id")
    private String suiteId;


    /**
     * 临时授权码,最长为512字节。用于获取企业永久授权码。10分钟内有效
     */
    @Schema(description = "临时授权码,最长为512字节。用于获取企业永久授权码。10分钟内有效")
    @TableField("auth_code")
    private String authCode;


    /**
     * 授权方的corpId
     */
    @Schema(description = "授权方的corpId")
    @TableField("auth_corp_id")
    private String authCorpId;


    /**
     * 回调通知类型
     */
    @Schema(description = "回调通知类型")
    @TableField("info_type")
    private String infoType;


    /**
     * 时间戳
     */
    @Schema(description = "时间戳")
    @TableField("time_stamp")
    private String timeStamp;


    /**
     * Ticket内容，最长为512字节
     */
    @Schema(description = "Ticket内容，最长为512字节")
    @TableField("suite_ticket")
    private String suiteTicket;


    /**
     * 构造授权链接指定的state参数，设定为租户客户编号
     */
    @Schema(description = "构造授权链接指定的state参数，设定为租户客户编号")
    @TableField("state")
    private String state;


    /**
     * 购买方corpid
     */
    @Schema(description = "购买方corpid")
    @TableField("paid_corp_id")
    private String paidCorpId;


    /**
     * 订单号，付费订单的唯一标志。服务商可据此拉取购买信息
     */
    @Schema(description = "订单号，付费订单的唯一标志。服务商可据此拉取购买信息")
    @TableField("order_id")
    private String orderId;


    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


    /**
     * 新增人
     */
    @Schema(description = "新增人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 新增时间
     */
    @Schema(description = "新增时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 最后编辑人
     */
    @Schema(description = "最后编辑人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 最后编辑时间
     */
    @Schema(description = "最后编辑时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
