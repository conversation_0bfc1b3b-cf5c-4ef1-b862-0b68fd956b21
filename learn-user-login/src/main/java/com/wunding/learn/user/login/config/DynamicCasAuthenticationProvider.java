package com.wunding.learn.user.login.config;

import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.login.util.TenantIdUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apereo.cas.client.validation.Cas20ServiceTicketValidator;
import org.springframework.security.cas.ServiceProperties;
import org.springframework.security.cas.authentication.CasAuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 支持动态ServiceProperties的CAS认证提供器
 */
@Slf4j
public class DynamicCasAuthenticationProvider extends CasAuthenticationProvider {

    private DynamicServicePropertiesFactory servicePropertiesFactory;
    
    private ParaFeign paraFeign;
    
    private String defaultCasServerUrl;

    private int tenantType;

    public DynamicCasAuthenticationProvider(int tenantType){
        this.tenantType = tenantType;
    }

    /**
     * 设置参数服务
     */
    public void setParaFeign(ParaFeign paraFeign) {
        this.paraFeign = paraFeign;
    }
    
    /**
     * 设置默认CAS服务器URL
     */
    public void setDefaultCasServerUrl(String defaultCasServerUrl) {
        this.defaultCasServerUrl = defaultCasServerUrl;
    }

    public void setServicePropertiesFactory(DynamicServicePropertiesFactory servicePropertiesFactory) {
        this.servicePropertiesFactory = servicePropertiesFactory;
    }
    
    /**
     * 根据租户ID获取CAS服务器URL
     */
    private String getCasServerUrl(String tenantId) {
        if (paraFeign != null) {
            try {
                String dynamicUrl = paraFeign.getParaValue(SystemConfigCodeEnum.CAS_LOGIN_SERVICE_URL.getCode());
                if (dynamicUrl != null && !dynamicUrl.isEmpty()) {
                    log.info("租户[{}]从参数服务获取CAS服务器URL: {}", tenantId, dynamicUrl);
                    return dynamicUrl;
                }
            } catch (Exception e) {
                log.error("从参数服务获取CAS服务器URL失败，使用默认值", e);
            }
        }
        
        log.info("使用配置文件中的CAS服务器URL: {}", defaultCasServerUrl);
        return defaultCasServerUrl;
    }

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        // 获取当前请求
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            
            // 动态创建ServiceProperties
            ServiceProperties serviceProperties = servicePropertiesFactory.createServiceProperties(request);
            log.info("DynamicCasAuthenticationProvider使用动态ServiceProperties, URL: {}", serviceProperties.getService());
            
            // 设置动态ServiceProperties
            this.setServiceProperties(serviceProperties);
            
            // 获取当前租户ID
            String tenantId = TenantIdUtil.getTenantId(tenantType,request);
            log.info("当前认证使用租户ID: {}", tenantId);
            
            // 根据租户ID创建对应的票据验证器
            String casServerUrl = getCasServerUrl(tenantId);
            // 使用自定义的JSON票据验证器替代标准的Cas20ServiceTicketValidator
            CustomJsonServiceTicketValidator ticketValidator = new CustomJsonServiceTicketValidator(casServerUrl);
            this.setTicketValidator(ticketValidator);
            log.info("DynamicCasAuthenticationProvider使用租户[{}]的自定义JSON票据验证器，URL: {}", tenantId, casServerUrl);
        }
        
        // 调用父类方法进行认证
        return super.authenticate(authentication);
    }
} 