package com.wunding.learn.user.login.dto;

import java.util.Map;

/**
 * CAS服务响应数据模型
 * 用于解析CAS serviceValidate接口返回的JSON响应
 *
 * <AUTHOR> Code
 */
public class CasServiceResponse {

    private ServiceResponse serviceResponse;

    public ServiceResponse getServiceResponse() {
        return serviceResponse;
    }

    public void setServiceResponse(ServiceResponse serviceResponse) {
        this.serviceResponse = serviceResponse;
    }

    public static class ServiceResponse {

        private AuthenticationSuccess authenticationSuccess;
        private AuthenticationFailure authenticationFailure;

        public AuthenticationSuccess getAuthenticationSuccess() {
            return authenticationSuccess;
        }

        public void setAuthenticationSuccess(AuthenticationSuccess authenticationSuccess) {
            this.authenticationSuccess = authenticationSuccess;
        }

        public AuthenticationFailure getAuthenticationFailure() {
            return authenticationFailure;
        }

        public void setAuthenticationFailure(AuthenticationFailure authenticationFailure) {
            this.authenticationFailure = authenticationFailure;
        }
    }

    public static class AuthenticationSuccess {

        private String user;
        private Map<String, Object> attributes;

        public String getUser() {
            return user;
        }

        public void setUser(String user) {
            this.user = user;
        }

        public Map<String, Object> getAttributes() {
            return attributes;
        }

        public void setAttributes(Map<String, Object> attributes) {
            this.attributes = attributes;
        }
    }

    public static class AuthenticationFailure {

        private String code;
        private String description;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
    }
}
