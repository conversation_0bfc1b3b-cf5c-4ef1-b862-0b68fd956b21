package com.wunding.learn.user.login.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * CAS密码验证响应DTO
 *
 * <AUTHOR>
 * @date 2024/12/26
 */
@Data
@Schema(name = "CasPasswordVerifyResponseDTO", description = "CAS密码验证响应")
public class CasPasswordVerifyResponseDTO {

    @Schema(description = "请求是否成功标识", example = "SUCCESS")
    private String code;

    @Schema(description = "详细信息")
    private CasPasswordVerifyDataDTO data;

    /**
     * 验证响应数据
     */
    @Data
    @Schema(name = "CasPasswordVerifyDataDTO", description = "CAS密码验证响应数据")
    public static class CasPasswordVerifyDataDTO {

        @Schema(description = "用户ID", example = "5c8edd4098c2714b7a2536ee")
        private String id;

        @Schema(description = "用户名", example = "110804")
        private String username;

        @Schema(description = "组织机构信息")
        private List<Map<String, String>> groupInfo;

        @Schema(description = "用户姓名", example = "杨中华")
        private String displayName;

        @Schema(description = "手机号", example = "***********")
        private String mobile;

        @Schema(description = "邮箱", example = "<EMAIL>")
        private String email;

        @Schema(description = "应用信息")
        private List<Map<String, String>> appInfo;

        @Schema(description = "账户名称列表")
        private List<String> accountName;

        @Schema(description = "是否启用", example = "true")
        private Boolean enabled;

        @Schema(description = "密码是否过期", example = "false")
        private Boolean pwdExpire;
    }
}
