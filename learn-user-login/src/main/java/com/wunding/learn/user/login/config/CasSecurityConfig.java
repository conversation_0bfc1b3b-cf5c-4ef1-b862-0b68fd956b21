package com.wunding.learn.user.login.config;

import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.util.http.WebUtil;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.login.filter.CustomCasAuthenticationFilter;
import com.wunding.learn.user.login.handler.CasAuthSuccessHandler1;
import com.wunding.learn.user.login.util.TenantIdUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apereo.cas.client.session.SingleSignOutFilter;
import org.apereo.cas.client.validation.Cas20ServiceTicketValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.cas.authentication.CasAuthenticationProvider;
import org.springframework.security.cas.web.CasAuthenticationFilter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.logout.LogoutFilter;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.security.web.authentication.logout.SimpleUrlLogoutSuccessHandler;

/**
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
@Slf4j
public class CasSecurityConfig {

    @Value("${cas.server.url}")
    private String casServerUrl;

    @Value("${cas.server.logout-url}")
    private String casLogoutUrl;

    @Value("${cas.client.service-url}")
    private String serviceUrl;

    @Resource
    private ParaFeign paraFeign;

    @Resource
    private UserDetailsService userDetailsService;

    @Value("${tenant.type}")
    private int tenantType;



    /**
     * 配置 Spring Security 的过滤器链
     */
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            .authorizeHttpRequests(authorize -> authorize
                .requestMatchers("/cas/casLogin").permitAll()
                .requestMatchers("/cas/casLogin/**").permitAll() // 允许带有任何路径参数的casLogin请求
                .requestMatchers("/cas/login").permitAll()
                .requestMatchers("/login").permitAll()
                .requestMatchers("/refreshToken").permitAll()
                .requestMatchers("/captcha/**").permitAll()
                .requestMatchers("/jsapiTicket").permitAll()
                .requestMatchers("/appInfo").permitAll()
                .requestMatchers("/bindSysUser").permitAll()
                .requestMatchers("/loginSkip").permitAll()
                .requestMatchers("/sso/authCode").permitAll()
                .requestMatchers("/sso/training/ip").permitAll()
                .requestMatchers("/sso/afterSales/login").permitAll()
                .requestMatchers("/sso/training/login").permitAll()
                .requestMatchers("/token").permitAll()
                .requestMatchers("/enroll/api/getWxUserPhone").permitAll()
                .requestMatchers("/enroll/api/register").permitAll()
                .requestMatchers("/weChatGetPhone").permitAll()
                .requestMatchers("/weChatGetOpenId").permitAll()
                .requestMatchers("/weChatLogin").permitAll()
                .requestMatchers("/getUserByPhone").permitAll()
                .requestMatchers("/actuator/**").permitAll()
                .requestMatchers("/captcha/captchaFlag").permitAll()



                //JWT 未配置好暂时需要放行
                .requestMatchers("/loginUserInfo").permitAll()
                .requestMatchers("/loginKey").permitAll()
                .requestMatchers("/**").permitAll()




                .requestMatchers("/swagger-ui.html", "/swagger-ui/**", "/v3/api-docs/**").permitAll()
                .anyRequest().authenticated()
            )
            .csrf(csrf -> csrf.disable())
            .addFilter(casAuthenticationFilter())
            .addFilterBefore(casLogoutFilter(), LogoutFilter.class)
            .addFilterBefore(singleSignOutFilter(), CasAuthenticationFilter.class);

        return http.build();
    }

    /**
     * CAS 认证过滤器
     */
    private CasAuthenticationFilter casAuthenticationFilter() {
        CustomCasAuthenticationFilter filter = new CustomCasAuthenticationFilter();
        filter.setAuthenticationManager(authenticationManager());
        filter.setFilterProcessesUrl("/cas/casLogin");
        
        // 手动设置DynamicServicePropertiesFactory避免循环依赖
        filter.setDynamicServicePropertiesFactory(dynamicServicePropertiesFactory());
        
        // 配置认证成功处理器，生成JWT令牌
        CasAuthSuccessHandler1 successHandler = new CasAuthSuccessHandler1();

        // 设置默认重定向URL（当没有已保存的请求时使用）
        successHandler.setDefaultTargetUrl("/cas/userinfo");
        filter.setAuthenticationSuccessHandler(successHandler);
        
        return filter;
    }

    /**
     * CAS 登出过滤器
     */
    private LogoutFilter casLogoutFilter() {
        // 获取当前租户ID
        String tenantId = TenantIdUtil.getTenantId(tenantType,WebUtil.getRequest());
        String logoutUrl = casLogoutUrl;
        if(StringUtils.isNotBlank(tenantId)){
            // 动态获取CAS服务器URL
            logoutUrl = getDynamicCasLogoutUrl(tenantId);
        }

        LogoutSuccessHandler logoutSuccessHandler = new SimpleUrlLogoutSuccessHandler();
        ((SimpleUrlLogoutSuccessHandler) logoutSuccessHandler).setDefaultTargetUrl(logoutUrl);

        LogoutFilter logoutFilter = new LogoutFilter(logoutSuccessHandler, new SecurityContextLogoutHandler());
        logoutFilter.setFilterProcessesUrl("/logout/cas");
        return logoutFilter;
    }

    /**
     * CAS 单点登出过滤器
     */
    private SingleSignOutFilter singleSignOutFilter() {
        SingleSignOutFilter singleSignOutFilter = new SingleSignOutFilter();
        singleSignOutFilter.setIgnoreInitConfiguration(true);
        return singleSignOutFilter;
    }

    /**
     * 创建动态ServiceProperties工厂
     */
    @Bean
    public DynamicServicePropertiesFactory dynamicServicePropertiesFactory() {
        // 创建新的工厂实例，传入默认serviceUrl和paraService
        return new DynamicServicePropertiesFactory(serviceUrl, paraFeign,tenantType);
    }

    /**
     * CAS 认证提供器
     */
    @Bean
    public CasAuthenticationProvider casAuthenticationProvider() {
        DynamicCasAuthenticationProvider provider = new DynamicCasAuthenticationProvider(tenantType);
        provider.setUserDetailsService(userDetailsService);
        provider.setKey("CAS_PROVIDER_LOCALHOST_8080");
        // 设置参数服务和默认CAS服务器URL
        provider.setParaFeign(paraFeign);
        provider.setDefaultCasServerUrl(casServerUrl);
        // 手动设置factory避免循环依赖
        provider.setServicePropertiesFactory(dynamicServicePropertiesFactory());
        
        // 设置默认的自定义JSON票据验证器，在afterPropertiesSet方法中必须有ticketValidator
        CustomJsonServiceTicketValidator ticketValidator = new CustomJsonServiceTicketValidator(casServerUrl);
        provider.setTicketValidator(ticketValidator);
        
        return provider;
    }
    
    /**
     * 从参数服务动态获取CAS服务器URL
     * @param tenantId 租户ID
     * @return 对应租户的CAS服务器URL
     */
    public String getDynamicCasServerUrl(String tenantId) {
        try {
            String dynamicUrl = paraFeign.getParaValue(SystemConfigCodeEnum.CAS_LOGIN_SERVICE_URL.getCode());
            if (dynamicUrl != null && !dynamicUrl.isEmpty()) {
                log.info("租户[{}]从参数服务获取CAS服务器URL: {}", tenantId, dynamicUrl);
                return dynamicUrl;
            }
        } catch (Exception e) {
            log.error("从参数服务获取CAS服务器URL失败，使用默认值", e);
        }
        
        log.info("使用配置文件中的CAS服务器URL: {}", casServerUrl);
        return casServerUrl;
    }

    public String getDynamicCasLoginUrl(String tenantId) {
        try {
            String dynamicUrl = paraFeign.getParaValue(SystemConfigCodeEnum.CAS_LOGIN_SERVICE_URL_LOGIN.getCode());
            if (dynamicUrl != null && !dynamicUrl.isEmpty()) {
                log.info("租户[{}]从参数服务获取CAS登录URL: {}", tenantId, dynamicUrl);
                return dynamicUrl;
            }
        } catch (Exception e) {
            log.error("从参数服务获取CAS登录URL失败，使用默认值", e);
        }

        log.info("使用配置文件中的CAS登录URL: {}", casServerUrl);
        return casServerUrl;
    }

    public String getDynamicCasLogoutUrl(String tenantId) {
        try {
            String dynamicUrl = casLogoutUrl;
            if(StringUtils.isNotBlank(tenantId)){
                // 动态获取CAS服务器URL
                dynamicUrl = paraFeign.getParaValue(SystemConfigCodeEnum.CAS_LOGIN_SERVICE_URL_LOGOUT.getCode());
            }
            if (dynamicUrl != null && !dynamicUrl.isEmpty()) {
                log.info("租户[{}]从参数服务获取CAS登出URL: {}", tenantId, dynamicUrl);
                return dynamicUrl;
            }
        } catch (Exception e) {
            log.error("从参数服务获取CAS登出URL失败，使用默认值", e);
        }

        log.info("使用配置文件中的CAS登出URL: {}", casServerUrl);
        return casServerUrl;
    }
    
    /**
     * 认证管理器
     * 注册 CasAuthenticationProvider 到 AuthenticationManager
     */
    @Bean
    public AuthenticationManager authenticationManager() {
        return new ProviderManager(Collections.singletonList(casAuthenticationProvider()));
    }
}