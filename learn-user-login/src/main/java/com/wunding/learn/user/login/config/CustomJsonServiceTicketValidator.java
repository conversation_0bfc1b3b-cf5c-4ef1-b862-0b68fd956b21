package com.wunding.learn.user.login.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wunding.learn.user.login.dto.CasServiceResponse;
import org.apereo.cas.client.authentication.AttributePrincipal;
import org.apereo.cas.client.authentication.AttributePrincipalImpl;
import org.apereo.cas.client.validation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 自定义JSON格式的CAS票据验证器
 * 用于处理返回JSON格式响应的CAS服务器
 *
 * <AUTHOR> Code
 */
public class CustomJsonServiceTicketValidator implements TicketValidator {

    private static final Logger log = LoggerFactory.getLogger(CustomJsonServiceTicketValidator.class);
    
    private final String casServerUrlPrefix;
    private final ObjectMapper objectMapper;
    private String encoding = "UTF-8";
    private boolean acceptAnyProxy = false;
    private String proxyCallbackUrl;
    private String proxyGrantingTicketStorageClass;
    private String renew = "false";
    
    /**
     * 构造函数
     * 
     * @param casServerUrlPrefix CAS服务器URL前缀
     */
    public CustomJsonServiceTicketValidator(String casServerUrlPrefix) {
        if (!StringUtils.hasText(casServerUrlPrefix)) {
            throw new IllegalArgumentException("casServerUrlPrefix cannot be null or empty");
        }
        this.casServerUrlPrefix = casServerUrlPrefix;
        this.objectMapper = new ObjectMapper();
        log.info("初始化CustomJsonServiceTicketValidator，CAS服务器URL: {}", casServerUrlPrefix);
    }
    
    @Override
    public Assertion validate(String ticket, String service) throws TicketValidationException {
        if (!StringUtils.hasText(ticket)) {
            throw new TicketValidationException("票据不能为空");
        }
        if (!StringUtils.hasText(service)) {
            throw new TicketValidationException("服务URL不能为空");
        }
        
        log.info("开始验证CAS票据，ticket: {}, service: {}", ticket, service);
        
        try {
            // 构建验证URL
            String validationUrl = buildValidationUrl(ticket, service);
            log.info("CAS票据验证URL: {}", validationUrl);
            
            // 发送HTTP请求获取响应
            String responseBody = sendValidationRequest(validationUrl);
            log.debug("CAS服务器响应: {}", responseBody);
            
            // 解析JSON响应
            return parseResponse(responseBody);
            
        } catch (Exception e) {
            log.error("CAS票据验证失败", e);
            throw new TicketValidationException("票据验证过程中发生错误: " + e.getMessage(), e);
        }
    }
    
    /**
     * 构建验证URL
     */
    private String buildValidationUrl(String ticket, String service) throws Exception {
        StringBuilder urlBuilder = new StringBuilder(casServerUrlPrefix);
        if (!casServerUrlPrefix.endsWith("/")) {
            urlBuilder.append("/");
        }
        urlBuilder.append("serviceValidate");
        urlBuilder.append("?ticket=").append(URLEncoder.encode(ticket, encoding));
        urlBuilder.append("&service=").append(URLEncoder.encode(service, encoding));
        urlBuilder.append("&format=JSON"); // 请求JSON格式响应
        
        if (StringUtils.hasText(proxyCallbackUrl)) {
            urlBuilder.append("&pgtUrl=").append(URLEncoder.encode(proxyCallbackUrl, encoding));
        }
        
        if ("true".equals(renew)) {
            urlBuilder.append("&renew=true");
        }
        
        return urlBuilder.toString();
    }
    
    /**
     * 发送验证请求
     */
    private String sendValidationRequest(String validationUrl) throws IOException {
        URL url = new URL(validationUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        try {
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000); // 5秒连接超时
            connection.setReadTimeout(10000);   // 10秒读取超时
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("User-Agent", "CustomJsonServiceTicketValidator/1.0");
            
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                throw new IOException("HTTP请求失败，响应码: " + responseCode);
            }
            
            // 读取响应内容
            StringBuilder response = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
            }
            
            return response.toString();
            
        } finally {
            connection.disconnect();
        }
    }
    
    /**
     * 解析JSON响应
     */
    private Assertion parseResponse(String responseBody) throws TicketValidationException {
        try {
            CasServiceResponse response = objectMapper.readValue(responseBody, CasServiceResponse.class);
            
            if (response.getServiceResponse() == null) {
                throw new TicketValidationException("无效的CAS响应格式：缺少serviceResponse节点");
            }
            
            CasServiceResponse.ServiceResponse serviceResponse = response.getServiceResponse();
            
            // 检查认证是否成功
            if (serviceResponse.getAuthenticationSuccess() != null) {
                return createSuccessAssertion(serviceResponse.getAuthenticationSuccess());
            } else if (serviceResponse.getAuthenticationFailure() != null) {
                CasServiceResponse.AuthenticationFailure failure = serviceResponse.getAuthenticationFailure();
                String errorMsg = String.format("CAS认证失败 - 错误码: %s, 描述: %s", 
                    failure.getCode(), failure.getDescription());
                log.error(errorMsg);
                throw new TicketValidationException(errorMsg);
            } else {
                throw new TicketValidationException("无效的CAS响应：既没有成功也没有失败信息");
            }
            
        } catch (Exception e) {
            log.error("解析CAS响应JSON失败", e);
            throw new TicketValidationException("解析CAS响应失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建成功的断言对象
     */
    private Assertion createSuccessAssertion(CasServiceResponse.AuthenticationSuccess authSuccess) {
        String principal = authSuccess.getUser();
        Map<String, Object> attributes = authSuccess.getAttributes();
        
        if (!StringUtils.hasText(principal)) {
            log.error("CAS响应中缺少用户名信息");
            principal = "未知用户";
        }
        
        log.info("CAS认证成功，用户: {}, 属性数量: {}", principal, 
            attributes != null ? attributes.size() : 0);
        
        // 创建AttributePrincipal
        AttributePrincipal attributePrincipal = new AttributePrincipalImpl(
            principal, 
            attributes != null ? attributes : new HashMap<>()
        );
        
        // 创建Assertion
        return new AssertionImpl(attributePrincipal);
    }
    
    // Getter和Setter方法
    public void setEncoding(String encoding) {
        this.encoding = encoding;
    }
    
    public void setAcceptAnyProxy(boolean acceptAnyProxy) {
        this.acceptAnyProxy = acceptAnyProxy;
    }
    
    public void setProxyCallbackUrl(String proxyCallbackUrl) {
        this.proxyCallbackUrl = proxyCallbackUrl;
    }
    
    public void setRenew(String renew) {
        this.renew = renew;
    }
}
