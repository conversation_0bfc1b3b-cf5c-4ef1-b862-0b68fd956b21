package com.wunding.learn.user.login.greater;

import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.user.api.dto.LoginUserInfo;
import com.wunding.learn.user.login.biz.ILoginBiz;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

/**
 * 第三方类型授权
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @date 2023/5/22
 */
@Component
@Slf4j
public class CasTokenGranter implements ITokenGranter {

    @Resource
    private ILoginBiz loginBiz;


    @Override
    public LoginUserInfo grant(GrantParameter grantParameter) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new BusinessException(UserErrorNoEnum.ERR_NEED_LOGIN);
        }

        String userId=null;
        if(StringUtils.isNotBlank(grantParameter.getOpenId())){
            log.info("获取 CAS 认证用户信息 前端传入userId: {}", grantParameter.getOpenId());
            userId = grantParameter.getOpenId();
        }
        if(StringUtils.isBlank(userId)){
            // 获取用户 ID
            userId = authentication.getName();
            log.info("获取 CAS 认证用户信息: {}", userId);

        }

        // 从业务服务中获取详细用户信息
        return loginBiz.getUserInfo(userId);
    }

}
