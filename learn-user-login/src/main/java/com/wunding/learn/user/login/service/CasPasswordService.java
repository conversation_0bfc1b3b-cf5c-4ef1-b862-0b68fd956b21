package com.wunding.learn.user.login.service;

import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.http.RestTemplateManager;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.user.api.dto.LoginUserInfo;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.login.config.CasPasswordConfig;
import com.wunding.learn.user.login.dto.CasPasswordUserInfoRequestDTO;
import com.wunding.learn.user.login.dto.CasPasswordUserInfoResponseDTO;
import com.wunding.learn.user.login.dto.CasPasswordVerifyRequestDTO;
import com.wunding.learn.user.login.dto.CasPasswordVerifyResponseDTO;
import com.wunding.learn.user.login.greater.LoginService;
import com.wunding.learn.user.login.util.CasPasswordCryptoUtil;
import com.wunding.learn.user.api.service.UserFeign;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * CAS密码登录服务类
 *
 * <AUTHOR>
 * @date 2024/12/26
 */
@Slf4j
@Service
public class CasPasswordService {

    @Resource
    private CasPasswordConfig casPasswordConfig;

    @Resource
    private LoginService loginService;

    @Resource
    private UserFeign userFeign;

    /**
     * 认证并获取用户信息
     *
     * @param username 用户名
     * @param password 密码
     * @return 登录用户信息
     */
    public LoginUserInfo authenticateAndGetUserInfo(String username, String password) {
        log.info("开始CAS密码登录认证，用户名: {}", maskUsername(username));

        // 1. 检查配置
        if (!casPasswordConfig.isConfigValid()) {
            log.error("CAS密码登录配置无效");
            throw new BusinessException(UserErrorNoEnum.ERR_CAS_PASSWORD_CONFIG_ERROR);
        }

        // 2. 调用第三方验证接口
        CasPasswordVerifyResponseDTO verifyResult = verifyUser(username, password);
        if (verifyResult == null || verifyResult.getData() == null) {
            log.error("第三方验证接口返回数据为空，用户名: {}", maskUsername(username));
            throw new BusinessException(UserErrorNoEnum.ERR_CAS_PASSWORD_VERIFY_FAIL);
        }

        String casUsername = verifyResult.getData().getUsername();
        if (StringUtils.isBlank(casUsername)) {
            log.error("第三方验证接口未返回员工用户名，用户名: {}", maskUsername(username));
            throw new BusinessException(UserErrorNoEnum.ERR_CAS_PASSWORD_VERIFY_FAIL);
        }

        log.info("第三方验证成功，员工用户名: {}", maskUsername(casUsername));

        // 3. 获取用户详细信息
        CasPasswordUserInfoResponseDTO userInfoResult = getUserInfo(casUsername);
        if (userInfoResult == null || userInfoResult.getData() == null) {
            log.error("获取用户信息失败，员工编号: {}", maskUsername(casUsername));
            throw new BusinessException(UserErrorNoEnum.ERR_CAS_PASSWORD_USER_INFO_ERROR);
        }

        // 4. 转换为系统内部用户信息
        LoginUserInfo loginUserInfo = convertToLoginUserInfo(userInfoResult.getData());
        log.info("CAS密码登录认证成功，用户ID: {}", loginUserInfo.getUser().getId());

        return loginUserInfo;
    }

    /**
     * 第三方用户验证
     *
     * @param username 用户名
     * @param password 密码
     * @return 验证结果
     */
    private CasPasswordVerifyResponseDTO verifyUser(String username, String password) {
        try {
            String verifyUrl = casPasswordConfig.getVerifyUrl();
            String authToken = casPasswordConfig.getAuthToken();

            // 加密密码
            String encryptedPassword = CasPasswordCryptoUtil.encryptPassword(password);

            // 构建请求
            CasPasswordVerifyRequestDTO request = new CasPasswordVerifyRequestDTO();
            request.setUsername(username);
            request.setPassword(encryptedPassword);

            // 设置请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", authToken);
            headers.put("Content-Type", "application/json");

            log.info("调用第三方验证接口，URL: {}, 用户名: {}", verifyUrl, maskUsername(username));

            // 发送请求
            String res = com.wunding.learn.common.util.http.JdkHttpUtils.doPost(verifyUrl,Map.of(), headers, JsonUtil.objToJson(request));

            // 添加调试日志 - 打印原始响应内容
            log.info("第三方验证接口原始响应: {}", res);

            // 检查响应是否为空
            if (StringUtils.isBlank(res)) {
                log.error("第三方验证接口返回空响应");
                throw new BusinessException(UserErrorNoEnum.ERR_CAS_PASSWORD_THIRD_PARTY_ERROR);
            }

            // 尝试解析 JSON
            CasPasswordVerifyResponseDTO response = null;
            try {
                response = JsonUtil.jsonToObj(res, CasPasswordVerifyResponseDTO.class);
                log.info("JSON 解析成功，响应对象: {}", JsonUtil.objToJson(response));
            } catch (Exception e) {
                log.error("JSON 解析失败，原始响应内容: [{}], 异常信息: {}", res, e.getMessage(), e);
                throw new BusinessException(UserErrorNoEnum.ERR_CAS_PASSWORD_THIRD_PARTY_ERROR);
            }

            if (response == null) {
                log.error("第三方验证接口返回为空");
                throw new BusinessException(UserErrorNoEnum.ERR_CAS_PASSWORD_THIRD_PARTY_ERROR);
            }

            // 检查响应状态
            if (!"SUCCESS".equals(response.getCode())) {
                log.error("第三方验证失败，响应码: {}, 消息: {}", response.getCode(), JsonUtil.objToJson(response.getData()));
                throw new BusinessException(UserErrorNoEnum.ERR_CAS_PASSWORD_VERIFY_FAIL);
            }

            log.info("第三方验证接口调用成功");
            return response;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("调用第三方验证接口异常", e);
            throw new BusinessException(UserErrorNoEnum.ERR_CAS_PASSWORD_THIRD_PARTY_ERROR);
        }
    }

    /**
     * 获取用户详细信息
     *
     * @param casUsername cas用户名
     * @return 用户信息
     */
    private CasPasswordUserInfoResponseDTO getUserInfo(String casUsername) {
        try {
            String userInfoUrl = casPasswordConfig.getUserInfoUrl();
            String authToken = casPasswordConfig.getAuthToken();

            // 构建请求
            CasPasswordUserInfoRequestDTO request = new CasPasswordUserInfoRequestDTO();
            request.setUsername(casUsername);

            log.info("调用用户信息接口，URL: {}, 员工编号: {}", userInfoUrl, maskUsername(casUsername));

            // 设置请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", authToken);
            headers.put("Content-Type", "application/json");

            // 发送请求
            String res = com.wunding.learn.common.util.http.JdkHttpUtils.doPost(userInfoUrl,Map.of(), headers, JsonUtil.objToJson(request));

            // 添加调试日志 - 打印原始响应内容
            log.info("用户信息接口原始响应: {}", res);

            // 检查响应是否为空
            if (StringUtils.isBlank(res)) {
                log.error("用户信息接口返回空响应");
                throw new BusinessException(UserErrorNoEnum.ERR_CAS_PASSWORD_USER_INFO_ERROR);
            }

            // 尝试解析 JSON
            CasPasswordUserInfoResponseDTO response = null;
            try {
                response = JsonUtil.jsonToObj(res, CasPasswordUserInfoResponseDTO.class);
                log.info("JSON 解析成功，响应对象: {}", JsonUtil.objToJson(response));
            } catch (Exception e) {
                log.error("JSON 解析失败，原始响应内容: [{}], 异常信息: {}", res, e.getMessage(), e);
                throw new BusinessException(UserErrorNoEnum.ERR_CAS_PASSWORD_USER_INFO_ERROR);
            }

            if (response == null) {
                log.error("用户信息接口返回为空");
                throw new BusinessException(UserErrorNoEnum.ERR_CAS_PASSWORD_USER_INFO_ERROR);
            }

            // 检查响应状态
            if (!"SUCCESS".equals(response.getCode())) {
                log.error("获取用户信息失败，响应码: {}, 消息: {}", response.getCode(), JsonUtil.objToJson(response.getData()));
                throw new BusinessException(UserErrorNoEnum.ERR_CAS_PASSWORD_USER_INFO_ERROR);
            }

            log.info("用户信息接口调用成功");
            return response;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("调用用户信息接口异常", e);
            throw new BusinessException(UserErrorNoEnum.ERR_CAS_PASSWORD_USER_INFO_ERROR);
        }
    }

/*
    public static void main(String[] args) {
        String json = "{\"data\":{\"attrs\":{\"EmpNo\":\"73773\"}}}";
        CasPasswordUserInfoResponseDTO casPasswordUserInfoResponseDTO = JsonUtil.jsonToObj(json,
            CasPasswordUserInfoResponseDTO.class);
        System.err.println(casPasswordUserInfoResponseDTO.getData().getAttrs().getEmpNo());
    }
*/

    /**
     * 转换为系统内部用户信息
     *
     * @param userData 第三方用户数据
     * @return 登录用户信息
     */
    private LoginUserInfo convertToLoginUserInfo(CasPasswordUserInfoResponseDTO.CasPasswordUserDataDTO userData) {

        if(userData == null || userData.getAttrs() == null || StringUtils.isBlank(userData.getAttrs().getEmpNo())){
            log.warn("第三方用户信息中未包含员工编号，无法找到系统中的用户");
            throw new BusinessException(UserErrorNoEnum.USER_NOT_EXISTS);
        }
        try {
            // 1. 首先尝试通过用户名查找系统中的用户
            UserDTO existingUser = userFeign.getUserByLoginName(userData.getAttrs().getEmpNo());
            if (existingUser != null) {
                log.info("找到系统中的用户，用户名: {}", maskUsername(userData.getUsername()));
                return loginService.userToLoginUserInfo(existingUser);
            }

            // 2. 如果系统中不存在该用户，可以考虑创建新用户或抛出异常
            // 这里根据业务需求决定是否允许自动创建用户
            log.warn("系统中未找到用户，用户名: {}", maskUsername(userData.getUsername()));
            throw new BusinessException(UserErrorNoEnum.USER_NOT_EXISTS);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("转换用户信息失败", e);
            throw new BusinessException(UserErrorNoEnum.ERR_CAS_PASSWORD_USER_INFO_ERROR);
        }
    }

    /**
     * 用户名脱敏
     */
    private String maskUsername(String username) {
        if (StringUtils.isBlank(username) || username.length() <= 2) {
            return "***";
        }
        return username.substring(0, 1) + "***" + username.substring(username.length() - 1);
    }


}
