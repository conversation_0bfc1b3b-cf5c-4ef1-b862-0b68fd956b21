package com.wunding.learn.user.login.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * CAS密码用户信息响应DTO
 *
 * <AUTHOR>
 * @date 2024/12/26
 */
@Data
@Schema(name = "CasPasswordUserInfoResponseDTO", description = "CAS密码用户信息响应")
public class CasPasswordUserInfoResponseDTO {

    @Schema(description = "响应码", example = "SUCCESS")
    private String code;

    @Schema(description = "用户数据")
    private CasPasswordUserDataDTO data;

    /**
     * 用户数据
     */
    @Data
    @Schema(name = "CasPasswordUserDataDTO", description = "CAS密码用户数据")
    public static class CasPasswordUserDataDTO {

        @Schema(description = "用户ID", example = "5c8edd4098c2714b7a2536ee")
        private String id;

        @Schema(description = "头像ID")
        private String avatarId;

        @Schema(description = "组织名称列表")
        private List<Map<String, String>> groupNames;

        @Schema(description = "组织ID列表")
        private List<String> groupIds;

        @Schema(description = "组织链")
        private List<String> groupChain;

        @Schema(description = "用户名", example = "110804")
        private String username;

        @Schema(description = "显示名称", example = "杨中华")
        private String displayName;

        @Schema(description = "手机号", example = "19800025713")
        private String mobile;

        @Schema(description = "邮箱", example = "<EMAIL>")
        private String email;

        @Schema(description = "用户API密钥")
        private String userApiKey;

        @Schema(description = "是否启用", example = "true")
        private Boolean enabled;

        @Schema(description = "证件号", example = "235896485652369444")
        private String certNo;

        @Schema(description = "扩展属性")
        private CasPasswordUserAttrsDTO attrs;
    }

    /**
     * 用户扩展属性
     */
    @Data
    @Schema(name = "CasPasswordUserAttrsDTO", description = "CAS密码用户扩展属性")
    public static class CasPasswordUserAttrsDTO {

        @Schema(description = "柜员号", example = "111111")
        private String tellerNo;

        @Schema(description = "员工编号", example = "52565412")
        @JsonProperty("EmpNo")
        private String empNo;
    }
}
