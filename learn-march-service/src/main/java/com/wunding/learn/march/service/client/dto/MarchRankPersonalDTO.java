package com.wunding.learn.march.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: EmigratedRankPersonalVo
 * @projectName learn
 * @description: 对应排行榜个人数据
 * @date 2022/2/249:56
 */
@Data
@Schema(name = "EmigratedRankPersonalDTO", description = "游戏排行榜个人数据返回对象")
public class MarchRankPersonalDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户总分数")
    private Integer sumScore;

    @Schema(description = "用户总耗时(秒)")
    private Long sumTime;

    @Schema(description = "用户排行")
    private Integer rank;

    @Schema(description = "用户头像地址")
    private String headImg;

    @Schema(description = "用户名字")
    private String userName;

    /**
     * 由于游戏应用可能不包含团队，这时就不展示团队排行榜
     */
    @Schema(description = "是否展示团队排行榜 0-否 1-是")
    private Integer showTeamRank;

}
