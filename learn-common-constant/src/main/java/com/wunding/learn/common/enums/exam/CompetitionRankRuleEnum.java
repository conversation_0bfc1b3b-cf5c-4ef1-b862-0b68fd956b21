package com.wunding.learn.common.enums.exam;

/**
 * 竞赛排名规则枚举类
 *
 * <AUTHOR>
 * @date 2023年10月20
 */
public enum CompetitionRankRuleEnum {

    /**
     * 默认
     */
    DEFAULT(0, "total_score"),

    /**
     * 按分数
     */
    BY_SCORE(1, "total_score"),

    /**
     * 按正确答题数
     */
    BY_CORRECT_NUMBER(2, "correct_number");

    private Integer key;

    private String column;

    CompetitionRankRuleEnum(int key, String column) {
        this.key = key;
        this.column = column;
    }

    public Integer getKey() {
        return key;
    }

    public String getColumn() {
        return column;
    }

    public static CompetitionRankRuleEnum valueOf(int ordinal) {
        if (ordinal >= 0 && ordinal < values().length) {
            return values()[ordinal];
        } else {
            return DEFAULT;
        }
    }
}
