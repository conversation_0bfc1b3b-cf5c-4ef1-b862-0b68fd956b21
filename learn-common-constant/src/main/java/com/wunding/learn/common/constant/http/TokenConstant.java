package com.wunding.learn.common.constant.http;

/**
 * Token配置常量.
 *
 * <AUTHOR>
 * @date 2022/02/21
 */
public class TokenConstant {

    private TokenConstant() {
        throw new UnsupportedOperationException("utility class");
    }

    public static final String HEADER_FEIGN_SOURCE_KEY = "header-feign-source";
    public static final String HEADER_FEIGN_SOURCE_VALUE = "feign";

    public static final String SIGN_KEY =
        "bladexisapowerfulmicroservicearchitectureupgradedandoptimizedfromacommercialproject";
    public static final String AVATAR = "avatar";
    public static final String HEADER = "learn-auth";
    public static final String BEARER = "Bearer ";
    public static final String ACCESS_TOKEN = "access_token";
    public static final String REFRESH_TOKEN = "refresh_token";
    public static final String TOKEN_TYPE = "token_type";
    public static final String EXPIRES_IN = "expires_in";
    public static final String ACCOUNT = "account";
    public static final String USER_ID = "user_id";
    public static final String ORG_ID = "org_id";
    public static final String DEVICE_ID = "deviceid";
    public static final String CLIENT_TYPE = "clienttype";
    public static final String ROLE_ID = "role_id";
    public static final String DEPT_ID = "dept_id";
    public static final String USER_NAME = "user_name";
    public static final String ROLE_NAME = "role_name";
    public static final String TENANT_ID = "tenant-id";
    public static final String OAUTH_ID = "oauth_id";
    public static final String CLIENT_ID = "client_id";
    public static final String LICENSE = "license";
    public static final String LICENSE_NAME = "powered by wunding";
    public static final String DEFAULT_AVATAR = "";
    public static final Integer AUTH_LENGTH = 7;
    public static final String MOBILE = "Mobile";
    public static final String IS_EXPORT = "1";
    public static final String WECHAT_ACCESS_TOKEN = "wechat_access_token";
    public static final String X_B3_TRACE_ID = "x-b3-traceid";
    public static final String HOST = "Host";
}
