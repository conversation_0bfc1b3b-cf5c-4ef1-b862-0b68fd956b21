package com.wunding.learn.reading.service.client.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 图书心得列表查询对象
 *
 * <AUTHOR>
 * @date 2022/09/06
 */
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class BookExpClientQuery extends BaseEntity {

    private static final long serialVersionUID = -7505619272960006977L;

    /**
     * 共读id
     */
    @Parameter(description = "共读id")
    @NotBlank(message = "共读id不能为空")
    private String activityId;

    /**
     * 共读图书id
     */
    @Parameter(description = "共读图书id")
    private String bookId;

    /**
     * 心得搜索关键字
     */
    @Parameter(description = "心得搜索关键字")
    private String searchKey;
}
