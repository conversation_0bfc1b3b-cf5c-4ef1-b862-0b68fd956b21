package com.wunding.learn.reading.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 共读打卡日历
 *
 * <AUTHOR>
 * @date 2022/09/06
 */
@Data
@Accessors(chain = true)
@Schema(name = "ReadingSignCalendarDTO", description = "共读打卡日历对象")
public class ReadingSignCalendarDTO {

    /**
     * id
     */
    @Schema(description = "打卡记录id")
    private String id;

    /**
     * 打卡日期
     */
    @Schema(description = "打卡日期")
    private String signTime;

}
