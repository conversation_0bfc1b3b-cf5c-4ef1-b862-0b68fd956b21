<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.business.view.service.mapper.StatTimeRegionMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.business.view.service.mapper.StatTimeRegionMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.business.view.service.model.StatTimeRegion">
            <!--@Table stat_time_region-->
                    <result column="access_date" jdbcType="TIMESTAMP"
                            property="accessDate"/>
                    <result column="org_id" jdbcType="VARCHAR"
                            property="orgId"/>
                    <result column="access_point1" jdbcType="INTEGER"
                            property="accessPoint1"/>
                    <result column="access_point2" jdbcType="INTEGER"
                            property="accessPoint2"/>
                    <result column="access_point3" jdbcType="INTEGER"
                            property="accessPoint3"/>
                    <result column="access_point4" jdbcType="INTEGER"
                            property="accessPoint4"/>
                    <result column="access_point5" jdbcType="INTEGER"
                            property="accessPoint5"/>
                    <result column="access_point6" jdbcType="INTEGER"
                            property="accessPoint6"/>
                    <result column="access_point7" jdbcType="INTEGER"
                            property="accessPoint7"/>
                    <result column="access_point8" jdbcType="INTEGER"
                            property="accessPoint8"/>
                    <result column="access_point9" jdbcType="INTEGER"
                            property="accessPoint9"/>
                    <result column="access_point10" jdbcType="INTEGER"
                            property="accessPoint10"/>
                    <result column="access_point11" jdbcType="INTEGER"
                            property="accessPoint11"/>
                    <result column="access_point12" jdbcType="INTEGER"
                            property="accessPoint12"/>
                    <result column="access_point13" jdbcType="INTEGER"
                            property="accessPoint13"/>
                    <result column="access_point14" jdbcType="INTEGER"
                            property="accessPoint14"/>
                    <result column="access_point15" jdbcType="INTEGER"
                            property="accessPoint15"/>
                    <result column="access_point16" jdbcType="INTEGER"
                            property="accessPoint16"/>
                    <result column="access_point17" jdbcType="INTEGER"
                            property="accessPoint17"/>
                    <result column="access_point18" jdbcType="INTEGER"
                            property="accessPoint18"/>
                    <result column="access_point19" jdbcType="INTEGER"
                            property="accessPoint19"/>
                    <result column="access_point20" jdbcType="INTEGER"
                            property="accessPoint20"/>
                    <result column="access_point21" jdbcType="INTEGER"
                            property="accessPoint21"/>
                    <result column="access_point22" jdbcType="INTEGER"
                            property="accessPoint22"/>
                    <result column="access_point23" jdbcType="INTEGER"
                            property="accessPoint23"/>
                    <result column="access_point24" jdbcType="INTEGER"
                            property="accessPoint24"/>
                    <result column="online_point1" jdbcType="INTEGER"
                            property="onlinePoint1"/>
                    <result column="online_point2" jdbcType="INTEGER"
                            property="onlinePoint2"/>
                    <result column="online_point3" jdbcType="INTEGER"
                            property="onlinePoint3"/>
                    <result column="online_point4" jdbcType="INTEGER"
                            property="onlinePoint4"/>
                    <result column="online_point5" jdbcType="INTEGER"
                            property="onlinePoint5"/>
                    <result column="online_point6" jdbcType="INTEGER"
                            property="onlinePoint6"/>
                    <result column="online_point7" jdbcType="INTEGER"
                            property="onlinePoint7"/>
                    <result column="online_point8" jdbcType="INTEGER"
                            property="onlinePoint8"/>
                    <result column="online_point9" jdbcType="INTEGER"
                            property="onlinePoint9"/>
                    <result column="online_point10" jdbcType="INTEGER"
                            property="onlinePoint10"/>
                    <result column="online_point11" jdbcType="INTEGER"
                            property="onlinePoint11"/>
                    <result column="online_point12" jdbcType="INTEGER"
                            property="onlinePoint12"/>
                    <result column="online_point13" jdbcType="INTEGER"
                            property="onlinePoint13"/>
                    <result column="online_point14" jdbcType="INTEGER"
                            property="onlinePoint14"/>
                    <result column="online_point15" jdbcType="INTEGER"
                            property="onlinePoint15"/>
                    <result column="online_point16" jdbcType="INTEGER"
                            property="onlinePoint16"/>
                    <result column="online_point17" jdbcType="INTEGER"
                            property="onlinePoint17"/>
                    <result column="online_point18" jdbcType="INTEGER"
                            property="onlinePoint18"/>
                    <result column="online_point19" jdbcType="INTEGER"
                            property="onlinePoint19"/>
                    <result column="online_point20" jdbcType="INTEGER"
                            property="onlinePoint20"/>
                    <result column="online_point21" jdbcType="INTEGER"
                            property="onlinePoint21"/>
                    <result column="online_point22" jdbcType="INTEGER"
                            property="onlinePoint22"/>
                    <result column="online_point23" jdbcType="INTEGER"
                            property="onlinePoint23"/>
                    <result column="online_point24" jdbcType="INTEGER"
                            property="onlinePoint24"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            access_date, org_id, access_point1, access_point2, access_point3, access_point4, access_point5, access_point6, access_point7, access_point8, access_point9, access_point10, access_point11, access_point12, access_point13, access_point14, access_point15, access_point16, access_point17, access_point18, access_point19, access_point20, access_point21, access_point22, access_point23, access_point24, online_point1, online_point2, online_point3, online_point4, online_point5, online_point6, online_point7, online_point8, online_point9, online_point10, online_point11, online_point12, online_point13, online_point14, online_point15, online_point16, online_point17, online_point18, online_point19, online_point20, online_point21, online_point22, online_point23, online_point24
        </sql>
        <select id="findTimeRegionStatByDate"
                resultType="com.wunding.learn.business.view.service.model.StatTimeRegion" useCache="false">
                select *
                from stat_time_region s
                where s.access_date = #{date}
        </select>

</mapper>
