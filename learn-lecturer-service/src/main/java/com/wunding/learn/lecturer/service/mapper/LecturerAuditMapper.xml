<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.lecturer.service.mapper.LecturerAuditMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.lecturer.service.mapper.LecturerAuditMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.lecturer.service.model.LecturerAudit">
            <!--@Table lecturer_audit-->
                    <result column="id" jdbcType="VARCHAR"
                            property="id"/>
                    <result column="user_id" jdbcType="VARCHAR"
                            property="userId"/>
                    <result column="reference_file_id" jdbcType="VARCHAR"
                            property="referenceFileId"/>
                    <result column="recommender_id" jdbcType="VARCHAR"
                            property="recommenderId"/>
                    <result column="status" jdbcType="BIT"
                            property="status"/>
                    <result column="audit_by" jdbcType="VARCHAR"
                            property="auditBy"/>
                    <result column="audit_time" jdbcType="TIMESTAMP"
                            property="auditTime"/>
                    <result column="remark" jdbcType="VARCHAR"
                            property="remark"/>
                    <result column="reson" jdbcType="VARCHAR"
                            property="reson"/>
                    <result column="create_by" jdbcType="VARCHAR"
                            property="createBy"/>
                    <result column="create_time" jdbcType="TIMESTAMP"
                            property="createTime"/>
                    <result column="update_by" jdbcType="VARCHAR"
                            property="updateBy"/>
                    <result column="update_time" jdbcType="TIMESTAMP"
                            property="updateTime"/>
                    <result column="is_del" jdbcType="BIT"
                            property="del"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, user_id, reference_file_id, recommender_id, status, audit_by, audit_time, remark, reson, create_by, create_time, update_by, update_time, is_del
        </sql>

</mapper>
