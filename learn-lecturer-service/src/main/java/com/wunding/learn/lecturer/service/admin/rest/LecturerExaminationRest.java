package com.wunding.learn.lecturer.service.admin.rest;


import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.lecturer.service.admin.dto.HistoryLecturerPageDTO;
import com.wunding.learn.lecturer.service.admin.dto.LecturerExaminationEditDTO;
import com.wunding.learn.lecturer.service.admin.dto.LecturerExaminationSaveDTO;
import com.wunding.learn.lecturer.service.admin.query.HistoryLecturerQuery;
import com.wunding.learn.lecturer.service.service.ILecturerExaminationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>  讲师授课明细表 前端控制器
 *
 * <AUTHOR> href="mailto:<EMAIL>">李毅慧</a>
 * @since 2022-05-11
 */
@RestController("lecturerLecturerExaminationRest")
@RequestMapping("${module.lecturer.contentPath:/}lecturerExamination")
@Tag(description = "讲师授课信息明细--课时", name = "LecturerExaminationRest")
public class LecturerExaminationRest {

    @Resource
    private ILecturerExaminationService lecturerExaminationService;

    @PostMapping("/save")
    @Operation(operationId = "save", summary = "保存讲师授课课时数据", description = "保存讲师授课信息课时数据")
    public Result<Void> saveExamination(@RequestBody LecturerExaminationSaveDTO examinationDTO) {
        lecturerExaminationService.saveLecturerExamination(examinationDTO);
        return Result.success();
    }

    @PostMapping("/update")
    @Operation(operationId = "update", summary = "更新讲师授课课时数据", description = "更新讲师授课信息课时数据")
    public Result<Void> saveExamination(@RequestBody LecturerExaminationEditDTO examinationDTO) {
        lecturerExaminationService.updateLecturerExamination(examinationDTO);
        return Result.success();
    }


    @GetMapping("/historyLecturer")
    @Operation(operationId = "historyLecturer", summary = "课程管理-课程/课件历史讲师", description = "课程管理-课程/课件历史讲师")
    public Result<PageInfo<HistoryLecturerPageDTO>> historyLecturer(
        @Valid @ParameterObject HistoryLecturerQuery historyLecturerQuery) {
        return Result.success(lecturerExaminationService.historyLecturer(historyLecturerQuery));
    }
}
