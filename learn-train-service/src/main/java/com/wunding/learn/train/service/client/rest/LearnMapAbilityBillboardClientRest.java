package com.wunding.learn.train.service.client.rest;

import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.dto.IdName;
import com.wunding.learn.train.service.biz.ILearnMapAbilityBiz;
import com.wunding.learn.train.service.client.dto.AbilityBillBoardAbilityPlanListDTO;
import com.wunding.learn.train.service.client.dto.AbilityBillBoardAbilityUserListDTO;
import com.wunding.learn.train.service.client.dto.AbilityBillBoardCategoryTypeListDTO;
import com.wunding.learn.train.service.client.dto.AbilityBillBoardOrgLearnReachListDTO;
import com.wunding.learn.train.service.client.dto.AbilityBillBoardPlanScheduleListDTO;
import com.wunding.learn.train.service.client.dto.AbilityBillBoardUserAbilityRadarListDTO;
import com.wunding.learn.train.service.client.dto.AbilityBillBoardUserLevelScoreDTO;
import com.wunding.learn.train.service.client.dto.AbilityRadarCategoryListDTO;
import com.wunding.learn.train.service.client.dto.LearnMapShapeApiListDTO;
import com.wunding.learn.train.service.client.query.AbilityPlanScheduleQuery;
import com.wunding.learn.train.service.client.query.LearnMapAbilityPlanClientQuery;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/4/24
 */
@RestController
@RequestMapping("${module.train.contentPath:/}learnMapAbilityBillboard/client")
@Tag(description = "学习地图能力数据看板", name = "LearnMapAbilityBillboardClientRest")
public class LearnMapAbilityBillboardClientRest {

    @Resource
    private ILearnMapAbilityBiz iLearnMapAbilityBiz;

    @GetMapping("/getMapShapeList")
    @Operation(operationId = "getMapShapeList_LearnMapAbilityBillboardClientRest", summary = "获取学习地图学习形式列表", description = "获取学习地图学习形式列表")
    public Result<List<LearnMapShapeApiListDTO>> getMapShapeList(
        @Parameter(description = "不为空时只查询启用的,为空时查询全部") @RequestParam(value = "isAll", required = false) Integer isAll) {
        return Result.success(iLearnMapAbilityBiz.getMapShapeList(isAll));
    }

    @GetMapping("/getAbilityMatrixList")
    @Operation(operationId = "getAbilityMatrixList_LearnMapAbilityBillboardClientRest", summary = "获取技能矩阵完成度列表", description = "获取技能矩阵完成度列表")
    public Result<List<AbilityBillBoardCategoryTypeListDTO>> getAbilityMatrixList(
        @Parameter(description = "组织id") @RequestParam("orgId") String orgId) {
        return Result.success(iLearnMapAbilityBiz.getAbilityMatrixList(orgId));
    }

    @GetMapping("/getLearnMapChooseByOrgId")
    @Operation(operationId = "getLearnMapChooseByOrgId_LearnMapAbilityBillboardClientRest", summary = "根据组织获取配置关注学习地图", description = "根据组织获取配置关注学习地图")
    public Result<List<IdName>> getLearnMapChooseByOrgId(
        @Parameter(description = "组织id") @RequestParam("orgId") String orgId) {
        return Result.success(iLearnMapAbilityBiz.getLearnMapChooseByOrgId(orgId));
    }

    @GetMapping("/getAbilityPlanList")
    @Operation(operationId = "getAbilityPlanList_LearnMapAbilityBillboardClientRest", summary = "获取培训计划列表", description = "获取培训计划列表")
    public Result<List<AbilityBillBoardAbilityPlanListDTO>> getAbilityPlanList(
        @Valid @ParameterObject LearnMapAbilityPlanClientQuery clientQuery) {
        return Result.success(iLearnMapAbilityBiz.getAbilityPlanList(clientQuery));
    }

    @GetMapping("/getOrgLearnReachList")
    @Operation(operationId = "getOrgLearnReachList_LearnMapAbilityBillboardClientRest", summary = "岗位知识学习达标率", description = "岗位知识学习达标率")
    public Result<List<AbilityBillBoardOrgLearnReachListDTO>> getOrgLearnReachList(
        @Valid @ParameterObject LearnMapAbilityPlanClientQuery clientQuery) {
        return Result.success(iLearnMapAbilityBiz.getOrgLearnReachList(clientQuery));
    }

    @GetMapping("/getUserViewIdentity")
    @Operation(operationId = "getUserViewIdentity_LearnMapAbilityBillboardClientRest", summary = "用户查看看板身份", description = "用户查看看板身份")
    public Result<List<IdName>> getUserViewIdentity() {
        return Result.success(iLearnMapAbilityBiz.getUserViewIdentity());
    }

    @GetMapping("/getChargeAbilityMatrixList")
    @Operation(operationId = "getChargeAbilityMatrixList_LearnMapAbilityBillboardClientRest", summary = "主管能力矩阵看板", description = "主管能力矩阵看板")
    public Result<List<AbilityBillBoardCategoryTypeListDTO>> getChargeAbilityMatrixList(
        @Parameter(description = "组织id") @RequestParam("orgId") String orgId) {
        return Result.success(iLearnMapAbilityBiz.getChargeAbilityMatrixList(orgId));
    }

    @GetMapping("/getChargeAbilityMatrixUserList")
    @Operation(operationId = "getChargeAbilityMatrixUserList_LearnMapAbilityBillboardClientRest", summary = "主管能力矩阵看板关联用户", description = "主管能力矩阵看板关联用户")
    public Result<List<AbilityBillBoardAbilityUserListDTO>> getChargeAbilityMatrixUserList(
        @Parameter(description = "组织id") @RequestParam("orgId") String orgId) {
        return Result.success(iLearnMapAbilityBiz.getChargeAbilityMatrixUserList(orgId));
    }

    @PostMapping("/saveFollowMap/{mapIds}/")
    @Operation(operationId = "saveFollowMap_LearnMapAbilityBillboardClientRest", summary = "保存用户关注学习地图", description = "保存用户关注学习地图")
    public Result<Void> saveFollowMap(
        @PathVariable @Parameter(description = "地图id列表,多个地图id已,号隔开") String mapIds) {
        iLearnMapAbilityBiz.saveFollowMap(mapIds);
        return Result.success();
    }

    @GetMapping("/getUserFollowMapList")
    @Operation(operationId = "getUserFollowMapList_LearnMapAbilityBillboardClientRest", summary = "获取用户专注的学习地图", description = "获取用户专注的学习地图")
    public Result<List<IdName>> getUserFollowMapList() {
        return Result.success(iLearnMapAbilityBiz.getUserFollowMapList());
    }

    @GetMapping("/getUserAbilityPortraitRadar")
    @Operation(operationId = "getUserAbilityPortraitRadar_LearnMapAbilityBillboardClientRest", summary = "获取用户能力画像雷达图信息", description = "获取用户能力画像雷达图信息")
    public Result<AbilityBillBoardUserAbilityRadarListDTO> getUserAbilityPortraitRadar(
        @Parameter(description = "查询用户id") String userId) {
        return Result.success(iLearnMapAbilityBiz.getAbilityRadarList(userId));
    }

    @GetMapping("/getUserMapAbilityList")
    @Operation(operationId = "getUserMapAbilityList_LearnMapAbilityBillboardClientRest", summary = "获取用户学习地图能力评价列表", description = "获取用户学习地图能力评价列表")
    public Result<List<AbilityRadarCategoryListDTO>> getUserMapAbilityList(
        @Parameter(description = "查询用户id") String userId) {
        return Result.success(iLearnMapAbilityBiz.getUserMapAbilityListByUserId(userId));
    }

    @GetMapping("/getAbilityUserLevelScoreList")
    @Operation(operationId = "getAbilityUserLevelScoreList_LearnMapAbilityBillboardClientRest", summary = "获取用户能力等级打分列表", description = "获取用户能力等级打分列表")
    public Result<List<AbilityBillBoardUserLevelScoreDTO>> getAbilityUserLevelScoreList(
        @Parameter(description = "查询用户id") String userId) {
        return Result.success(iLearnMapAbilityBiz.getAbilityUserLevelScoreList(userId));
    }

    @PostMapping("/saveOrUpdateAbilityUserLevelScore")
    @Operation(operationId = "saveOrUpdateAbilityUserLevelScore_LearnMapAbilityBillboardClientRest", summary = "提交用户能力等级打分选择", description = "提交用户能力等级打分选择")
    public Result<Void> saveOrUpdateAbilityUserLevelScore(
        @RequestBody @Valid List<AbilityBillBoardUserLevelScoreDTO> scoreList) {
        iLearnMapAbilityBiz.saveOrUpdateAbilityUserLevelScore(scoreList);
        return Result.success();
    }

    @GetMapping("/getAbilityPlanScheduleList")
    @Operation(operationId = "getAbilityPlanScheduleList_LearnMapAbilityBillboardClientRest", summary = "培训计划进度计划列表", description = "培训计划进度列表")
    public Result<List<AbilityBillBoardPlanScheduleListDTO>> getAbilityPlanScheduleList(
        @Valid @ParameterObject AbilityPlanScheduleQuery abilityPlanScheduleQuery) {
        return Result.success(iLearnMapAbilityBiz.getAbilityPlanScheduleList(abilityPlanScheduleQuery.getType(),
            abilityPlanScheduleQuery.getMapIdList(), abilityPlanScheduleQuery.getEndDate()));
    }

}
