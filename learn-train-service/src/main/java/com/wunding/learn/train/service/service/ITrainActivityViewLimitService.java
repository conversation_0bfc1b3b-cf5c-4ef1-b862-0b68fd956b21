package com.wunding.learn.train.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.train.service.model.TrainActivityViewLimit;

/**
 * <p> 课程总下发范围表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
public interface ITrainActivityViewLimitService extends IService<TrainActivityViewLimit> {


    /**
     * 检查下发范围
     *
     * @param userId     用户id
     * @param resourceId 资源id
     * @return boolean
     */
    boolean checkViewLimit(String userId, String resourceId);
}
