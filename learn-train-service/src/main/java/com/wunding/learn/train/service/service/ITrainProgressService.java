package com.wunding.learn.train.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.train.service.client.dto.TrainProgressDTO;
import com.wunding.learn.train.service.model.TrainProgress;
import java.util.Collection;
import java.util.List;

/**
 * <p> 项目完成表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @since 2023-04-11
 */
public interface ITrainProgressService extends IService<TrainProgress> {


    /**
     * 根据培训项获取进度
     *
     * @param trainIds
     * @return
     */
    List<TrainProgressDTO> findProgressByIds(List<String> trainIds);


    /**
     * 批量查询用户进度
     *
     * @param trainIdList
     * @param userId
     * @return
     */
    List<TrainProgress> findUserProgressList(List<String> trainIdList, String userId);

    /**
     * 是否加入培训项目
     *
     * @param trainId
     * @param userId
     * @return
     */
    Boolean isJoinTrain(String trainId, String userId);


    /**
     * 插入培训项目
     *
     * @param trainId
     * @param userId
     * @return
     */
    TrainProgress saveProgress(String trainId, String userId);


    /**
     * 删除培训项目进度
     *
     * @param trainId
     * @param userId
     */
    void deleteTrainProgress(String trainId, String userId);


    /**
     * 检测培训项目进度
     *
     * @param trainId
     * @param userId
     * @return
     */
    int checkTrainActivityProgressIsAllFinish(String trainId, String userId);

    /**
     * 更新培训项目进度完成状态
     *
     * @param ids
     * @param finishStatus
     */
    void updateTrainProgressFinishStatus(Collection<String> ids, Integer finishStatus);

    /**
     * 获取人员id
     *
     * @param trainId    培训项目id
     * @param activityId 活动ia
     * @return {@link String}
     */
    List<String> queryUserIds(String trainId, String activityId);
}
