<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.excitation.service.mapper.UserGoldCoinMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.excitation.service.mapper.UserGoldCoinMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.excitation.service.model.UserGoldCoin">
            <!--@Table user_gold_coin-->
                    <id column="user_id" jdbcType="VARCHAR" property="userId"/>
                    <result column="num" jdbcType="BIGINT"
                            property="num"/>
                    <result column="remain_num" jdbcType="DECIMAL"
                            property="remainNum"/>
                    <result column="origin_num" jdbcType="DECIMAL"
                            property="originNum"/>
        </resultMap>

        <resultMap id="UserGoldCoinPageDTOMap" type="com.wunding.learn.excitation.service.admin.dto.UserGoldCoinPageDTO">
            <result column="remain_num" jdbcType="DECIMAL" property="num" />
            <result column="convertible_num" jdbcType="DECIMAL" property="convertibleNum" />
            <result column="id" jdbcType="VARCHAR" property="userId" />
            <association property="userInfo" javaType="com.wunding.learn.excitation.service.admin.dto.UserInfoDTO">
                <result column="id" jdbcType="VARCHAR" property="id"/>
                <result column="type" jdbcType="INTEGER" property="type"/>
                <result column="full_name" jdbcType="VARCHAR" property="name"/>
                <result column="telephone" jdbcType="VARCHAR" property="phone"/>
                <result column="login_name" jdbcType="VARCHAR" property="loginName"/>
                <result column="member_org_id" jdbcType="VARCHAR" property="memberOrgId"/>
                <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
                <result column="user_type" jdbcType="VARCHAR" property="type"/>
            </association>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            user_id, num, remain_num, origin_num
        </sql>

        <select id="userGoldCoinList" resultMap="UserGoldCoinPageDTOMap"
          parameterType="com.wunding.learn.excitation.service.admin.query.UserGoldCoinPageQuery" useCache="false">
                SELECT
                    c.id, c.full_name, c.telephone, c.login_name, b.member_org_id,  d.org_name,a.remain_num, a.convertible_num, c.user_type
                FROM
                        user_gold_coin a
                                LEFT join member b on a.user_id = b.user_id and b.is_del = 0
                                inner join sys_user c on a.user_id = c.id and c.is_del = 0
                                left join sys_org d on c.org_id = d.id
                <where>
                    <if test="userIdCollection != null">
                        and a.user_id in
                        <foreach collection="userIdCollection" open="(" item="userId" separator="," close=")">
                            #{userId}
                        </foreach>
                    </if>
                    <if test="phone != null and phone != ''">
                        and c.telephone like concat(concat('%', #{phone}), '%')
                    </if>
                    <if test="type != null">
                        and c.user_type = #{type}
                    </if>
                    <if test="memberOrgId != null and memberOrgId != ''">
                        and b.member_org_id = #{memberOrgId}
                    </if>
                    <if test="orgId != null and orgId != ''">
                        and d.level_path like concat(#{orgLevelPath}, '%')
                    </if>
                </where>
            order by a.convertible_num desc, a.user_id
        </select>
</mapper>
