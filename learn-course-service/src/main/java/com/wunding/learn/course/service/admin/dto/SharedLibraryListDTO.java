package com.wunding.learn.course.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 课程共享库列表返回
 *
 * <AUTHOR>
 * @date 2022-05-10
 */
@Data
@Schema(name = "SharedLibraryListDTO", description = "课程共享库列表返回数据对象")
public class SharedLibraryListDTO implements Serializable {

    /**
     * 课程ID
     */
    @Schema(description = "课程ID")
    private String id;

    /**
     * 课程名称
     */
    @Schema(description = "课程名称")
    private String courseName;

    /**
     * 课程分类
     */
    @Schema(description = "课程分类")
    private String courseCateTypeName;

    /**
     * 课件数量
     */
    @Schema(description = "课件数量")
    private Integer cwCount;

    /**
     * 共享组织
     */
    @Schema(description = "共享组织")
    private String orgName;

    /**
     * 发布人
     */
    @Schema(description = "发布人")
    private String publishBy;

    /**
     * 发布时间
     */
    @Schema(description = "发布时间")
    private Date publishTime;

    /**
     * 是否发布
     */
    @Schema(description = "是否发布 0.未发布 1.已发布")
    private Integer isPublish;

    /**
     * 是否推荐
     */
    @Schema(description = "是否推荐 0.否 1.是")
    private Integer isRecommend;

    @Schema(description = "部门全称 orgPath")
    private String orgPath;
}
