package com.wunding.learn.course.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * @author: chenjinneng
 * @create: 2023-12-28
 **/
@Data
@Schema(name = "AbilityChooseCourseDTO", description = "关联课程列表")
public class AbilityChooseCourseDTO {

    @Schema(description = "课程id")
    private String courseId;

    @Schema(description = "课程编码")
    private String courseNo;

    @Schema(description = "课程名称")
    private String courseName;

    @Schema(description = "课时（分钟）")
    private double courseTime;

    @Schema(description = "课件数量")
    private int coursewareNum;

    @Schema(description = "状态，0-未发布，1-已发布")
    private int isPublish;

    @Schema(description = "添加人")
    private String createBy;

    @Schema(description = "添加时间")
    private Date createTime;

    @Schema(description = "发布人")
    private String publishBy;

    @Schema(description = "发布时间")
    private Date publishTime;
}
