<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.course.service.mapper.CourseShareRecordMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.course.service.mapper.CourseShareRecordMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.course.service.model.CourseShareRecord">
        <!--@Table course_share_record-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="course_id" jdbcType="VARCHAR"
          property="courseId"/>
        <result column="view_type" jdbcType="TINYINT"
          property="viewType"/>
        <result column="view_limit_id" jdbcType="BIGINT"
          property="viewLimitId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        course_id,
        view_type,
        view_limit_id
    </sql>
</mapper>
