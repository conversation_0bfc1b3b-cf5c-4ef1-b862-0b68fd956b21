package com.wunding.learn.common.util.net;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Properties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <p>SFTPUtils 单元测试类
 * 使用 Mockito 模拟 JSch、Session 和 ChannelSftp 依赖，测试 SFTPUtils 的所有方法。 不需要真实的SFTP服务器，运行速度快，覆盖所有方法的成功和失败场景。
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-06-30
 */
@ExtendWith(MockitoExtension.class)
class SFTPUtilsTest {

    private static final String TEST_HOST = "test.example.com";
    private static final int TEST_PORT = 22;
    private static final String TEST_USERNAME = "testuser";
    private static final String TEST_PASSWORD = "testpass";
    @TempDir
    private Path tempDir;
    private String TEST_LOCAL_FILE;
    private String TEST_REMOTE_FILE;

    @Mock
    private Session mockSession;

    @Mock
    private ChannelSftp mockChannelSftp;

    private SFTPUtils sftpUtils;

    @BeforeEach
    void setUp() throws IOException {
        sftpUtils = new SFTPUtils();
        TEST_LOCAL_FILE = tempDir.resolve("test.txt").toString();
        TEST_REMOTE_FILE = "/remote/test.txt";

        // 创建测试文件
        Files.write(tempDir.resolve("test.txt"), "test content".getBytes());
    }

    @Test
    void testConnect_Success() throws Exception {
        // Given
        JSch mockJSch = mock(JSch.class);
        try (MockedStatic<JSch> jschMock = mockStatic(JSch.class)) {
            jschMock.when(JSch::new).thenReturn(mockJSch);
            when(mockJSch.getSession(TEST_USERNAME, TEST_HOST, TEST_PORT)).thenReturn(mockSession);
            when(mockSession.openChannel("sftp")).thenReturn(mockChannelSftp);

            // When
            sftpUtils.connect(TEST_HOST, TEST_PORT, TEST_USERNAME, TEST_PASSWORD);

            // Then
            verify(mockJSch).getSession(TEST_USERNAME, TEST_HOST, TEST_PORT);
            verify(mockSession).setPassword(TEST_PASSWORD);
            verify(mockSession).setConfig(any(Properties.class));
            verify(mockSession).connect();
            verify(mockSession).openChannel("sftp");
            verify(mockChannelSftp).connect();
        }
    }

    @Test
    void testConnect_SessionConnectFailure() throws Exception {
        // Given
        JSch mockJSch = mock(JSch.class);
        try (MockedStatic<JSch> jschMock = mockStatic(JSch.class)) {
            when(mockJSch.getSession(TEST_USERNAME, TEST_HOST, TEST_PORT)).thenReturn(mockSession);

            // 设置异常 - 在实际调用时抛出异常
            doThrow(new JSchException("Connection failed")).when(mockSession).connect();

            // When & Then
            JSchException exception = assertThrows(JSchException.class, () ->
                sftpUtils.connect(TEST_HOST, TEST_PORT, TEST_USERNAME, TEST_PASSWORD));

            assertEquals("Connection failed", exception.getMessage());
        }
    }

    @Test
    void testConnect_ChannelOpenFailure() throws Exception {
        // Given
        JSch mockJSch = mock(JSch.class);
        try (MockedStatic<JSch> jschMock = mockStatic(JSch.class)) {
            jschMock.when(JSch::new).thenReturn(mockJSch);
            when(mockJSch.getSession(TEST_USERNAME, TEST_HOST, TEST_PORT)).thenReturn(mockSession);

            // 设置异常 - 在打开channel时抛出异常
            doThrow(new JSchException("Channel open failed")).when(mockSession).openChannel("sftp");

            // When & Then
            JSchException exception = assertThrows(JSchException.class, () ->
                sftpUtils.connect(TEST_HOST, TEST_PORT, TEST_USERNAME, TEST_PASSWORD));

            assertEquals("Channel open failed", exception.getMessage());
        }
    }

    @Test
    void testConnect_ChannelConnectFailure() throws Exception {
        // Given
        JSch mockJSch = mock(JSch.class);
        try (MockedStatic<JSch> jschMock = mockStatic(JSch.class)) {
            jschMock.when(JSch::new).thenReturn(mockJSch);
            when(mockJSch.getSession(TEST_USERNAME, TEST_HOST, TEST_PORT)).thenReturn(mockSession);
            when(mockSession.openChannel("sftp")).thenReturn(mockChannelSftp);

            // 设置异常 - 在channel连接时抛出异常
            doThrow(new JSchException("Channel connect failed")).when(mockChannelSftp).connect();

            // When & Then
            JSchException exception = assertThrows(JSchException.class, () ->
                sftpUtils.connect(TEST_HOST, TEST_PORT, TEST_USERNAME, TEST_PASSWORD));

            assertEquals("Channel connect failed", exception.getMessage());
        }
    }

    @Test
    void testUploadFile_Success() throws Exception {
        // Given
        setupConnectedSFTPWithReflection();

        // When
        sftpUtils.uploadFile(TEST_LOCAL_FILE, TEST_REMOTE_FILE);

        // Then
        verify(mockChannelSftp).put(any(FileInputStream.class), eq(TEST_REMOTE_FILE));
    }

    @Test
    void testUploadFile_SftpException() throws Exception {
        // Given
        setupConnectedSFTPWithReflection();
        doThrow(new SftpException(1, "Upload failed")).when(mockChannelSftp)
            .put(any(FileInputStream.class), eq(TEST_REMOTE_FILE));

        // When & Then
        assertThrows(SftpException.class, () ->
            sftpUtils.uploadFile(TEST_LOCAL_FILE, TEST_REMOTE_FILE));
    }

    @Test
    void testUploadFile_IOException() throws Exception {
        // Given
        setupConnectedSFTPWithReflection();

        // When & Then
        assertThrows(IOException.class, () ->
            sftpUtils.uploadFile("/nonexistent/file.txt", TEST_REMOTE_FILE));
    }

    @Test
    void testDownloadFile_Success() throws Exception {
        // Given
        setupConnectedSFTPWithReflection();
        String downloadPath = tempDir.resolve("download.txt").toString();

        // When
        sftpUtils.downloadFile(TEST_REMOTE_FILE, downloadPath);

        // Then
        verify(mockChannelSftp).get(eq(TEST_REMOTE_FILE), any(FileOutputStream.class));
    }

    @Test
    void testDownloadFile_SftpException() throws Exception {
        // Given
        setupConnectedSFTPWithReflection();
        String downloadPath = tempDir.resolve("download2.txt").toString();
        doThrow(new SftpException(1, "Download failed")).when(mockChannelSftp)
            .get(eq(TEST_REMOTE_FILE), any(FileOutputStream.class));

        // When & Then
        assertThrows(SftpException.class, () ->
            sftpUtils.downloadFile(TEST_REMOTE_FILE, downloadPath));
    }

    @Test
    void testDeleteFile_Success() throws Exception {
        // Given
        setupConnectedSFTPWithReflection();

        // When
        sftpUtils.deleteFile(TEST_REMOTE_FILE);

        // Then
        verify(mockChannelSftp).rm(TEST_REMOTE_FILE);
    }

    @Test
    void testDeleteFile_SftpException() throws Exception {
        // Given
        setupConnectedSFTPWithReflection();
        doThrow(new SftpException(1, "Delete failed")).when(mockChannelSftp).rm(TEST_REMOTE_FILE);

        // When & Then
        assertThrows(SftpException.class, () ->
            sftpUtils.deleteFile(TEST_REMOTE_FILE));
    }

    @Test
    void testDisconnect_BothConnected() throws Exception {
        // Given
        setupConnectedSFTPWithReflection();
        when(mockChannelSftp.isConnected()).thenReturn(true);
        when(mockSession.isConnected()).thenReturn(true);

        // When
        sftpUtils.disconnect();

        // Then
        verify(mockChannelSftp).disconnect();
        verify(mockSession).disconnect();
    }

    @Test
    void testDisconnect_OnlySessionConnected() throws Exception {
        // Given
        setupConnectedSFTPWithReflection();
        when(mockChannelSftp.isConnected()).thenReturn(false);
        when(mockSession.isConnected()).thenReturn(true);

        // When
        sftpUtils.disconnect();

        // Then
        verify(mockChannelSftp, never()).disconnect();
        verify(mockSession).disconnect();
    }

    @Test
    void testDisconnect_NeitherConnected() throws Exception {
        // Given
        setupConnectedSFTPWithReflection();
        when(mockChannelSftp.isConnected()).thenReturn(false);
        when(mockSession.isConnected()).thenReturn(false);

        // When
        sftpUtils.disconnect();

        // Then
        verify(mockChannelSftp, never()).disconnect();
        verify(mockSession, never()).disconnect();
    }

    @Test
    void testDisconnect_NullObjects() {
        // Given - SFTPUtils with null session and channel

        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> sftpUtils.disconnect());
    }

    /**
     * 使用反射设置已连接的SFTP环境，避免静态方法模拟的问题
     */
    private void setupConnectedSFTPWithReflection() throws Exception {
        // 使用反射直接设置 session 和 channelSftp 字段
        Field sessionField = SFTPUtils.class.getDeclaredField("session");
        sessionField.setAccessible(true);
        sessionField.set(sftpUtils, mockSession);

        Field channelSftpField = SFTPUtils.class.getDeclaredField("channelSftp");
        channelSftpField.setAccessible(true);
        channelSftpField.set(sftpUtils, mockChannelSftp);
    }
}
