# SFTPUtils 测试说明

本目录包含了 `SFTPUtils` 类的完整单元测试套件。

## 测试文件说明

### SFTPUtilsSimpleTest.java (推荐)
- **类型**: 简化单元测试
- **描述**: 专注于核心业务逻辑测试，避免复杂的构造函数模拟
- **特点**:
  - 不需要真实的SFTP服务器
  - 运行速度快，测试稳定
  - 使用反射直接设置依赖，避免静态方法模拟
  - 覆盖核心文件操作功能
  - 简单易维护

### SFTPUtilsTest.java (备用)
- **类型**: 完整单元测试
- **描述**: 包含连接测试的完整测试套件
- **特点**:
  - 包含连接参数验证测试
  - 更全面的测试覆盖
  - 可能需要处理静态方法模拟的复杂性

## 运行测试

### 运行单元测试

单元测试可以直接运行，不需要任何外部依赖：

```bash
# 运行所有单元测试
mvn test -Dtest=SFTPUtilsTest

# 运行特定的测试方法
mvn test -Dtest=SFTPUtilsTest#testConnect_Success
```

## 测试覆盖的功能

### 单元测试覆盖
- ✅ 连接SFTP服务器（成功/失败）
- ✅ 断开SFTP连接
- ✅ 文件上传（成功/失败）
- ✅ 文件下载（成功/失败）
- ✅ 文件删除（成功/失败）
- ✅ 异常处理测试
- ✅ 空对象处理测试

### 测试场景详情

#### 连接测试
- `testConnect_Success`: 测试成功连接到SFTP服务器
- `testConnect_SessionConnectFailure`: 测试Session连接失败场景
- `testConnect_ChannelOpenFailure`: 测试Channel打开失败场景
- `testConnect_ChannelConnectFailure`: 测试Channel连接失败场景

#### 文件上传测试
- `testUploadFile_Success`: 测试成功上传文件
- `testUploadFile_SftpException`: 测试上传时SFTP异常
- `testUploadFile_IOException`: 测试上传时IO异常（文件不存在）

#### 文件下载测试
- `testDownloadFile_Success`: 测试成功下载文件
- `testDownloadFile_SftpException`: 测试下载时SFTP异常

#### 文件删除测试
- `testDeleteFile_Success`: 测试成功删除文件
- `testDeleteFile_SftpException`: 测试删除时SFTP异常

#### 断开连接测试
- `testDisconnect_BothConnected`: 测试Session和Channel都连接时的断开
- `testDisconnect_OnlySessionConnected`: 测试只有Session连接时的断开
- `testDisconnect_NeitherConnected`: 测试都未连接时的断开
- `testDisconnect_NullObjects`: 测试空对象时的断开（不应抛异常）

## 技术实现说明

### 模拟策略
1. **JSch模拟**: 使用 `MockedStatic` 模拟JSch构造函数
2. **Session和ChannelSftp模拟**: 使用 `@Mock` 注解创建模拟对象
3. **反射注入**: 使用反射直接设置私有字段，避免复杂的静态方法模拟

### 依赖管理
测试使用以下依赖：
- JUnit 5 (Jupiter)
- Mockito (包括静态方法模拟)
- JSch (SFTP客户端库)

## 注意事项

1. **测试隔离**: 每个测试方法都是独立的，不依赖其他测试的执行结果
2. **异常处理**: 测试覆盖了各种异常场景，确保错误处理的正确性
3. **资源管理**: 测试验证了连接的正确断开和资源释放
4. **参数验证**: 测试验证了方法调用时参数的正确传递

## 测试最佳实践

1. **优先运行单元测试**: 单元测试运行快，应该作为主要的测试手段
2. **持续集成**: 在CI/CD流水线中运行这些测试
3. **代码覆盖率**: 这些测试提供了SFTPUtils类的完整代码覆盖
4. **维护性**: 测试代码结构清晰，易于维护和扩展

## 扩展建议

如果需要添加更多功能测试，可以考虑：
1. 添加目录操作测试（如果SFTPUtils扩展了目录功能）
2. 添加文件权限测试
3. 添加大文件传输测试
4. 添加并发操作测试

## 故障排除

如果测试失败，请检查：
1. 依赖版本是否正确
2. Mockito版本是否支持静态方法模拟
3. 测试环境是否正确配置
