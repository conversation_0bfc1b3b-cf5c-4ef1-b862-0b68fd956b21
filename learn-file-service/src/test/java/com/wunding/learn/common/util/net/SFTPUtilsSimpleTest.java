package com.wunding.learn.common.util.net;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Path;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * SFTPUtils 简化单元测试类
 * 
 * 专注于测试核心业务逻辑，避免复杂的构造函数模拟
 * 使用反射直接设置依赖，测试文件操作的核心功能
 * 
 * <AUTHOR>
 * @date 2024/12/30
 */
@ExtendWith(MockitoExtension.class)
class SFTPUtilsSimpleTest {

    private static final String TEST_REMOTE_FILE = "/remote/test.txt";

    @TempDir
    private Path tempDir;
    
    private String testLocalFile;

    @Mock
    private Session mockSession;

    @Mock
    private ChannelSftp mockChannelSftp;

    private SFTPUtils sftpUtils;

    @BeforeEach
    void setUp() throws Exception {
        sftpUtils = new SFTPUtils();
        testLocalFile = tempDir.resolve("test.txt").toString();
        
        // 创建测试文件
        Files.write(tempDir.resolve("test.txt"), "test content".getBytes());
        
        // 使用反射设置已连接状态
        setupMockConnection();
    }

    @Test
    void testUploadFile_Success() throws Exception {
        // When
        sftpUtils.uploadFile(testLocalFile, TEST_REMOTE_FILE);

        // Then
        verify(mockChannelSftp).put(any(FileInputStream.class), eq(TEST_REMOTE_FILE));
    }

    @Test
    void testUploadFile_SftpException() throws Exception {
        // Given
        doThrow(new SftpException(1, "Upload failed"))
            .when(mockChannelSftp).put(any(FileInputStream.class), eq(TEST_REMOTE_FILE));

        // When & Then
        assertThrows(SftpException.class, () ->
            sftpUtils.uploadFile(testLocalFile, TEST_REMOTE_FILE));
    }

    @Test
    void testUploadFile_FileNotFound() {
        // When & Then
        assertThrows(IOException.class, () ->
            sftpUtils.uploadFile("/nonexistent/file.txt", TEST_REMOTE_FILE));
    }

    @Test
    void testDownloadFile_Success() throws Exception {
        // Given
        String downloadPath = tempDir.resolve("download.txt").toString();

        // When
        sftpUtils.downloadFile(TEST_REMOTE_FILE, downloadPath);

        // Then
        verify(mockChannelSftp).get(eq(TEST_REMOTE_FILE), any(FileOutputStream.class));
    }

    @Test
    void testDownloadFile_SftpException() throws Exception {
        // Given
        String downloadPath = tempDir.resolve("download2.txt").toString();
        doThrow(new SftpException(1, "Download failed"))
            .when(mockChannelSftp).get(eq(TEST_REMOTE_FILE), any(FileOutputStream.class));

        // When & Then
        assertThrows(SftpException.class, () ->
            sftpUtils.downloadFile(TEST_REMOTE_FILE, downloadPath));
    }

    @Test
    void testDeleteFile_Success() throws Exception {
        // When
        sftpUtils.deleteFile(TEST_REMOTE_FILE);

        // Then
        verify(mockChannelSftp).rm(TEST_REMOTE_FILE);
    }

    @Test
    void testDeleteFile_SftpException() throws Exception {
        // Given
        doThrow(new SftpException(1, "Delete failed"))
            .when(mockChannelSftp).rm(TEST_REMOTE_FILE);

        // When & Then
        assertThrows(SftpException.class, () ->
            sftpUtils.deleteFile(TEST_REMOTE_FILE));
    }

    @Test
    void testDisconnect_Success() throws Exception {
        // Given
        when(mockChannelSftp.isConnected()).thenReturn(true);
        when(mockSession.isConnected()).thenReturn(true);

        // When & Then
        assertDoesNotThrow(() -> sftpUtils.disconnect());
        
        verify(mockChannelSftp).disconnect();
        verify(mockSession).disconnect();
    }

    @Test
    void testDisconnect_AlreadyDisconnected() throws Exception {
        // Given
        when(mockChannelSftp.isConnected()).thenReturn(false);
        when(mockSession.isConnected()).thenReturn(false);

        // When & Then
        assertDoesNotThrow(() -> sftpUtils.disconnect());
    }

    @Test
    void testDisconnect_NullObjects() {
        // Given - 创建新的SFTPUtils实例（没有设置连接）
        SFTPUtils newSftpUtils = new SFTPUtils();

        // When & Then - 应该不抛出异常
        assertDoesNotThrow(() -> newSftpUtils.disconnect());
    }

    @Test
    void testConnectionState() throws Exception {
        // Given - 验证mock连接已设置
        Field sessionField = SFTPUtils.class.getDeclaredField("session");
        sessionField.setAccessible(true);
        Session session = (Session) sessionField.get(sftpUtils);
        
        Field channelField = SFTPUtils.class.getDeclaredField("channelSftp");
        channelField.setAccessible(true);
        ChannelSftp channel = (ChannelSftp) channelField.get(sftpUtils);
        
        // Then
        assertNotNull(session);
        assertNotNull(channel);
    }

    /**
     * 使用反射设置模拟连接
     */
    private void setupMockConnection() throws Exception {
        Field sessionField = SFTPUtils.class.getDeclaredField("session");
        sessionField.setAccessible(true);
        sessionField.set(sftpUtils, mockSession);

        Field channelSftpField = SFTPUtils.class.getDeclaredField("channelSftp");
        channelSftpField.setAccessible(true);
        channelSftpField.set(sftpUtils, mockChannelSftp);
    }
}
