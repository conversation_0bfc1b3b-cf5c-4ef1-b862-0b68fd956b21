package com.wunding.learn.promotedgame.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022/2/8 15:00
 * @description 作业提交保存对象
 */
@Data
@Schema(name = "EmigratedHomeworkSubmitSaveDTO", description = "作业提交保存对象")
public class EmigratedHomeworkSubmitSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "闯关id")
    @NotBlank(message = "闯关id不能为空")
    private String emigratedId;

    @Schema(description = "作业id")
    @NotBlank(message = "作业id不能为空")
    private String homeworkId;

    @Schema(description = "我的提交内容")
    private String content;

    // 文件id为空则是修改操作 再判断文件路径和名称是替换还是删除
    @Schema(description = "文件id")
    private String fileId;

    @Schema(description = "文件临时路径")
    private String path;

    @Schema(description = "文件名称")
    private String fileName;

}