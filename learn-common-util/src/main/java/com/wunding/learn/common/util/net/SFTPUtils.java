package com.wunding.learn.common.util.net;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Properties;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SFTPUtils {

    private Session session;
    private ChannelSftp channelSftp;

    /**
     * 连接到SFTP服务器
     *
     * @param host     主机地址
     * @param port     端口（默认22）
     * @param username 用户名
     * @param password 密码
     */
    public void connect(String host, int port, String username, String password) throws JSchException {
        JSch jsch = new JSch();
        session = jsch.getSession(username, host, port);
        session.setPassword(password);
        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no");
        session.setConfig(config);
        session.connect();
        channelSftp = (ChannelSftp) session.openChannel("sftp");
        channelSftp.connect();
    }

    /**
     * 上传文件到SFTP服务器
     *
     * @param localFilePath  本地文件路径
     * @param remoteFilePath 远程文件路径（如 "/home/<USER>/remote.txt"）
     */
    public void uploadFile(String localFilePath, String remoteFilePath) throws SftpException, IOException {
        try (FileInputStream fis = new FileInputStream(localFilePath)) {
            channelSftp.put(fis, remoteFilePath);
        }
    }

    /**
     * 下载文件从SFTP服务器
     *
     * @param remoteFilePath 远程文件路径
     * @param localFilePath  本地文件路径
     */
    public void downloadFile(String remoteFilePath, String localFilePath) throws SftpException, IOException {
        try (FileOutputStream fos = new FileOutputStream(localFilePath)) {
            channelSftp.get(remoteFilePath, fos);
        }
    }

    /**
     * 删除SFTP服务器上的文件
     *
     * @param remoteFilePath 远程文件路径
     */
    public void deleteFile(String remoteFilePath) throws SftpException {
        channelSftp.rm(remoteFilePath);
    }

    /**
     * 关闭连接
     */
    public void disconnect() {
        if (channelSftp != null && channelSftp.isConnected()) {
            channelSftp.disconnect();
        }
        if (session != null && session.isConnected()) {
            session.disconnect();
        }
    }
}