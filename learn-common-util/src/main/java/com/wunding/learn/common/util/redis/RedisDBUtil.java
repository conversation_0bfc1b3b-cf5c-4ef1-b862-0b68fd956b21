package com.wunding.learn.common.util.redis;


import com.wunding.learn.common.constant.redis.CacheRedisDbConst;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.util.http.WebUtil;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Objects;
import java.util.Optional;
import org.springframework.util.Assert;

/**
 * Redis键值生成工具类
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @date 2021/3/5
 */
public class RedisDBUtil {

    private RedisDBUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static final String CACHE_NAME = "L";

    public static final String CACHE_NAME_PREFIX = CACHE_NAME + "::";

    public static final String KEY_SEPARATOR = ":";

    public static String getKey(String... keys) {
        Assert.notEmpty(keys, "缓存的键值不能为空!");
        StringBuilder returnKey = new StringBuilder();
        for (String key : keys) {
            if (key.endsWith(KEY_SEPARATOR)) {
                returnKey.append(KEY_SEPARATOR).append(key, 0, key.length() - 1);
            } else {
                returnKey.append(KEY_SEPARATOR).append(key);
            }
        }
        return returnKey.substring(1);
    }

    /**
     * 返回整体key
     *
     * @param keys 除CacheName以外的
     * @return 包含CacheName的整体key
     */
    public static String getFullKey(String... keys) {
        String serverName = "";
        // 如果是首页缓存，区分内外网
        if (keys.length > 0 && Objects.equals(keys[0], CacheRedisDbConst.CACHE)) {
            HttpServletRequest request = WebUtil.getRequest();
            serverName = Optional.ofNullable(request)
                .map(ServletRequest::getServerName)
                .orElse("");
        }
        return CACHE_NAME_PREFIX + serverName + UserThreadContext.getTenantId() + ":" + getKey(keys);
    }
}
