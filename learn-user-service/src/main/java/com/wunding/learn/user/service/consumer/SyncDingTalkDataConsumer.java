package com.wunding.learn.user.service.consumer;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rabbitmq.client.Channel;
import com.wunding.learn.common.constant.other.Oauth2EntryTypeEnum;
import com.wunding.learn.common.constant.redis.RedisKeyEnum;
import com.wunding.learn.common.constant.user.UserRedisKeyConstant;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.mq.event.user.DingTalkEvent;
import com.wunding.learn.common.mq.util.ConsumerAckUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.user.api.dto.third.dingtalk.DepartmentDTO.DeptGetResponse;
import com.wunding.learn.user.service.admin.dto.SyncIdentityDTO;
import com.wunding.learn.user.service.admin.dto.SyncOrgDTO;
import com.wunding.learn.user.service.admin.dto.SyncUserDTO;
import com.wunding.learn.user.service.biz.IThirdAppSyncBiz;
import com.wunding.learn.user.service.model.ThirdAppConfig;
import com.wunding.learn.user.service.service.IOrgDingTalkService;
import com.wunding.learn.user.service.service.IPostDingTalkService;
import com.wunding.learn.user.service.service.IThirdAppConfigService;
import com.wunding.learn.user.service.service.IUserDingTalkService;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Argument;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 钉钉同步组织架构消费者
 *
 * <AUTHOR> href="mailto:<EMAIL>">gcz</a>
 * @since 2023-08-29
 */
@Component
@Slf4j
public class SyncDingTalkDataConsumer {

    @Resource
    private IOrgDingTalkService orgDingTalkService;
    @Resource
    @SuppressWarnings("rawtypes")
    private RedisTemplate redisTemplate;
    @Resource
    private IUserDingTalkService userDingTalkService;
    @Resource
    private IPostDingTalkService postDingTalkService;
    @Resource
    private IThirdAppConfigService thirdAppConfigService;
    @Resource
    private IThirdAppSyncBiz thirdAppSyncBiz;

    /**
     * 钉钉数据刷新队列
     */
    public static final String DING_TALK_REFRESH_AUTH_EVENT_CONSUMER_QUEUE = "DingTalkRefreshAuthEventConsumerQueue";

    /**
     * 钉钉同步数据消费者
     */
    @RabbitListener(autoStartup = "false",
        bindings = @QueueBinding(
            value = @Queue(
                value = DING_TALK_REFRESH_AUTH_EVENT_CONSUMER_QUEUE,
                arguments = {@Argument(name = "x-single-active-consumer", value = "true",type = "java.lang.Boolean")}
            ),
            exchange = @Exchange(value = DingTalkEvent.DING_TALK_EXCHANGE, type = ExchangeTypes.TOPIC),
            key = DingTalkEvent.DingTalkRoutingKeyConstants.AGENCY_CALLBACK_REFRESH_AUTH_EVENT),
        id = "dingTalkRefreshAuthEventConsumer")
    @SuppressWarnings("unchecked")
    public void dingTalkRefreshAuthEventConsumer(DingTalkEvent dingTalkEvent,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        log.info("dingTalkRefreshAuthEventConsumer receive event :{} ", JsonUtil.objToJson(dingTalkEvent));
        try {
            UserThreadContext.setTenantId(dingTalkEvent.getTenantCustomerCode());
            UserThreadContext.setUserId("admin");

            // 获取第三方应用配置-钉钉
            ThirdAppConfig thirdAppConfig = thirdAppConfigService.getOne(
                new LambdaUpdateWrapper<ThirdAppConfig>().eq(ThirdAppConfig::getAppType,
                    Oauth2EntryTypeEnum.DING_TALK.getType()));

            // 未配置或未开启同步，无需处理
            if (thirdAppConfig == null || thirdAppConfig.getEnableSync() == 0) {
                ConsumerAckUtil.basicAck(dingTalkEvent,channel,deliveryTag, false);
                return;
            }
            // 全量同步处理，对非同步人员数据进行禁用
            if (thirdAppConfig.getSyncLogic() == 0) {
                thirdAppSyncBiz.disableUser(Oauth2EntryTypeEnum.DING_TALK, "", 1);
            }
            // 同步钉钉数据到系统
            List<DeptGetResponse> list = (List<DeptGetResponse>) redisTemplate.opsForHash().get(
                RedisKeyEnum.getBizFullKey(UserRedisKeyConstant.THIRD_APP_ORGANIZATIONAL_KEY,
                    Oauth2EntryTypeEnum.DING_TALK.getKey()), UserThreadContext.getTenantId());
            if (null != list) {
                syncData(list, thirdAppConfig.getAccountGenerationLogic());
            }
            // 同步完毕，更新状态
            if (GeneralJudgeEnum.NEGATIVE.getValue().equals(thirdAppConfig.getUserSyncStatus())) {
                thirdAppConfig.setUserSyncStatus(GeneralJudgeEnum.CONFIRM.getValue());
                thirdAppConfig.setUpdateBy(UserThreadContext.getUserId());
                thirdAppConfig.setUpdateTime(new Date());
                thirdAppConfigService.updateById(thirdAppConfig);
            }

        } catch (Exception e) {
            log.error("钉钉同步业务处理失败", e);
        } finally {
            UserThreadContext.remove();
            ConsumerAckUtil.basicAck(dingTalkEvent,channel,deliveryTag, false);
        }
    }

    /**
     * 同步数据
     *
     * @param list                   钉钉数据
     * @param accountGenerationLogic 账户生成逻辑：0-中文名拼音转化 1-使用账号同步
     */
    @SuppressWarnings("unchecked")
    private void syncData(List<DeptGetResponse> list, Integer accountGenerationLogic) {
        // 保存钉钉部门数据
        List<SyncOrgDTO> syncOrgList = orgDingTalkService.batchInsertData(list);
        // 保存钉钉岗位数据
        List<SyncIdentityDTO> syncPostList = postDingTalkService.batchInsertData(list);
        // 保存钉钉员工数据
        List<SyncUserDTO> syncUserList = userDingTalkService.batchInsertData(list, syncPostList,
            accountGenerationLogic);

        // 执行同步逻辑
        thirdAppSyncBiz.syncOrgData(syncOrgList, Oauth2EntryTypeEnum.DING_TALK);
        thirdAppSyncBiz.syncIdentityData(syncPostList, Oauth2EntryTypeEnum.DING_TALK);
        thirdAppSyncBiz.syncUserData(syncUserList, Oauth2EntryTypeEnum.DING_TALK);

        // 保存本次同步的组织，人员数据到redis中
        if (!CollectionUtils.isEmpty(syncOrgList)) {
            redisTemplate.opsForHash().put(RedisKeyEnum.getBizFullKey(UserRedisKeyConstant.THIRD_APP_HISTORY_ORG_KEY,
                Oauth2EntryTypeEnum.DING_TALK.getKey()), UserThreadContext.getTenantId(), syncOrgList);
        }
        if (!CollectionUtils.isEmpty(syncUserList)) {
            redisTemplate.opsForHash().put(RedisKeyEnum.getBizFullKey(UserRedisKeyConstant.THIRD_APP_HISTORY_USER_KEY,
                Oauth2EntryTypeEnum.DING_TALK.getKey()), UserThreadContext.getTenantId(), syncUserList);
        }
    }

}
