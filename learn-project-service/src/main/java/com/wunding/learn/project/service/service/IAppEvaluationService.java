package com.wunding.learn.project.service.service;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.BasePageQuery;
import com.wunding.learn.common.dto.PublishDTO;
import com.wunding.learn.evaluation.api.dto.EvaluationListDTO;
import com.wunding.learn.project.service.admin.dto.EvaluationEditDTO;
import com.wunding.learn.project.service.admin.dto.EvaluationSaveDTO;
import com.wunding.learn.project.service.admin.dto.LecturerEvaluationListDTO;
import com.wunding.learn.project.service.admin.query.EvaluationListQuery;
import com.wunding.learn.project.service.admin.query.LecturerEvaluationListQuery;
import com.wunding.learn.project.service.client.dto.EvaluateInfoDTO;
import com.wunding.learn.project.service.client.dto.EvaluateLearnerPageDTO;
import com.wunding.learn.project.service.client.dto.EvaluateLecturerPageDTO;
import org.springframework.scheduling.annotation.Async;

/**
 * <p> 培训评估表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">赖卓成</a>
 * @since 2022-07-19
 */
public interface IAppEvaluationService {

    /**
     * 评估列表
     *
     * @param evaluationListQuery
     * @return
     */
    PageInfo<EvaluationListDTO> listDetail(EvaluationListQuery evaluationListQuery);

    /**
     * 删除评估
     *
     * @param ids
     */
    void deleteEvaluations(String ids);

    /**
     * 发布评估/取消发布
     *
     * @param publishDTO
     */
    void publish(PublishDTO publishDTO);

    /**
     * 编辑时 评估预览
     *
     * @param id
     * @return
     */
    EvaluationEditDTO evaluationInfo(String id);

    /**
     * 添加/编辑评估
     *
     * @param evaluationSaveDTO
     */
    void saveOrUpdate(EvaluationSaveDTO evaluationSaveDTO);

    /**
     * 客户端-讲师/管理员获取项目评估列表
     *
     * @param id            学习项目id
     * @param basePageQuery 基本页面查询参数
     * @return {@link PageInfo}<{@link EvaluateLecturerPageDTO}>
     */
    PageInfo<EvaluateLecturerPageDTO> getLecturerEvaluatePage(String id, BasePageQuery basePageQuery);


    /**
     * 学员获取项目评估列表
     *
     * @param id            学习项目id
     * @param basePageQuery 基本页面查询参数
     * @return {@link PageInfo}<{@link EvaluateLecturerPageDTO}>
     */
    PageInfo<EvaluateLearnerPageDTO> getLearnerEvaluatePage(String id, BasePageQuery basePageQuery);

    /**
     * 获取评估详情
     *
     * @param id         id
     * @param evaluateId 评估id
     * @param bizId      业务id
     * @return {@link EvaluateInfoDTO}
     */
    EvaluateInfoDTO getEvaluateInfo(String id, String evaluateId, String bizId);

    /**
     * 讲师授课明细评估列表
     *
     * @param lecturerEvaluationListQuery{@Link LecturerEvaluationListQuery}
     * @return
     */
    PageInfo<LecturerEvaluationListDTO> lecturerEvalList(LecturerEvaluationListQuery lecturerEvaluationListQuery);

    /**
     * 导出讲师授课明细评估查询列表的数据
     *
     * @param lecturerEvaluationListQuery
     * @return
     */
    @Async
    void export(LecturerEvaluationListQuery lecturerEvaluationListQuery);

    EvaluationEditDTO getEvaluationListDTO(EvaluationListQuery evaluationListQuery);

    /**
     * 维护评估下发
     *
     * @param evaluationId 评估id
     * @param viewLimitId  下发id
     */
    void handleViewLimit(String evaluationId, Long viewLimitId);
}
