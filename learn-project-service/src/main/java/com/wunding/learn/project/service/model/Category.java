package com.wunding.learn.project.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 分类管理表
 *
 * <AUTHOR> href="mailto:<EMAIL>">赖卓成</a>
 * @since 2022-08-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("category")
@Schema(name = "Category对象", description = "分类管理表")
public class Category implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 分类ID
     */
    @Schema(description = "分类ID")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 分类名称
     */
    @Schema(description = "分类名称")
    @TableField("category_name")
    private String categoryName;


    /**
     * 上级分类ID
     */
    @Schema(description = "上级分类ID")
    @TableField("parent_id")
    private String parentId;


    /**
     * 分类类型
     */
    @Schema(description = "分类类型")
    @TableField("category_type")
    private String categoryType;


    /**
     * 分类层级
     */
    @Schema(description = "分类层级")
    @TableField("category_level")
    private Integer categoryLevel;


    /**
     * 全路径名
     */
    @Schema(description = "全路径名")
    @TableField("level_path")
    private String levelPath;


    /**
     * 描述
     */
    @Schema(description = "描述")
    @TableField("description")
    private String description;


    /**
     * 是否系统内置顶
     */
    @Schema(description = "是否系统内置顶")
    @TableField("sys_defined")
    private Integer sysDefined;


    /**
     * 是否启用该分类：
     */
    @Schema(description = "是否启用该分类：")
    @TableField("is_display")
    private Integer isDisplay;


    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    @TableField("is_available")
    private Integer isAvailable;


    /**
     * 排序
     */
    @Schema(description = "排序")
    @TableField("sort_no")
    private Integer sortNo;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    @TableField("is_del")
    private Integer isDel;


    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @TableField("customer_id")
    private String customerId;


    /**
     * 是否集团模式id
     */
    @Schema(description = "是否集团模式id")
    @TableField("org_id")
    private String orgId;


}
