package com.wunding.learn.project.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @Author: aixinrong
 * @Date: 2022/7/12 13:39
 */
@Data
@Schema(name = "EvalQuestionDTO", description = "评估问题对象")
public class EvalQuestionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "题目id")
    private String id;

    @Schema(description = "题目序号")
    private Integer questionNo;

    @Schema(description = "题目标题")
    private String questionDesc;

    @Schema(description = "题目类型", hidden = true)
    private Integer questionType;

    @Schema(description = "题目类型名称")
    private String questionTypeName;

    @Schema(description = "选项集合")
    private List<EvalQuestionItemDTO> questionOptionList;

    @Schema(description = "主观题的所有回答内容")
    private List<EvalReplyDTO> blankItemReply;

}
