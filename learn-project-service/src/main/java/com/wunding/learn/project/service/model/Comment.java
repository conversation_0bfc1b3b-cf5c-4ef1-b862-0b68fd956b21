package com.wunding.learn.project.service.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 项目评论表
 *
 * <AUTHOR> href="mailto:<EMAIL>">赖卓成</a>
 * @since 2022-07-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("project_comment")
@Schema(name = "Comment对象", description = "项目评论表")
public class Comment implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 编号
     */
    @Schema(description = "编号")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 项目编号
     */
    @Schema(description = "项目编号")
    @TableField("pro_id")
    private String proId;


    /**
     * 评论人
     */
    @Schema(description = "评论人")
    @TableField("comment_by")
    private String commentBy;


    /**
     * 评论时间
     */
    @Schema(description = "评论时间")
    @TableField("comment_time")
    private Date commentTime;


    /**
     * 评论内容
     */
    @Schema(description = "评论内容")
    @TableField("content")
    private String content;


    /**
     * 是否删除 0否1是
     */
    @Schema(description = "是否删除 0否1是")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


    /**
     * 父级评论id
     */
    @Schema(description = "父级评论id")
    @TableField("parent_id")
    private String parentId;


}
