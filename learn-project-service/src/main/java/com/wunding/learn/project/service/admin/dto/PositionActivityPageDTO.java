package com.wunding.learn.project.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @program: mlearn
 * @description: <p>岗位发展计划活动列表分页对象</p>
 * @author: 赖卓成
 * @create: 2022-07-12 13:56
 **/
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "PositionActivityPageDTO", description = "岗位发展计划活动列表分页对象")
public class PositionActivityPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 岗位发展计划活动id
     */
    @Schema(description = "岗位发展计划活动id")
    private String id;

    /**
     * 活动类型
     */
    @Schema(description = "活动类型")
    private Integer activityType;

    /**
     * 活动编号
     */
    @Schema(description = "活动编号")
    private String activityNo;

    /**
     * 活动名称
     */
    @Schema(description = "活动名称")
    private String activityName;

    /**
     * 资源名称
     */
    @Schema(description = "资源名称")
    private String resourceName;

    /**
     * 发布状态 0未发布 1已发布
     */
    @Schema(description = "发布状态 0未发布 1已发布")
    private Integer isPublish;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String createBy;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String orgName;

    /**
     * 创建日期
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortNo;

    /**
     * 逻辑删除
     */
    @Schema(description = "逻辑删除")
    private Integer isDel;

    @Schema(description = "部门全称")
    private String orgPath;
}
