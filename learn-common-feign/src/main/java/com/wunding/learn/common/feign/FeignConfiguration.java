package com.wunding.learn.common.feign;


import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.constant.http.SecureConstant;
import com.wunding.learn.common.constant.http.TokenConstant;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.http.WebUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.Util;
import feign.codec.ErrorDecoder;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/2/25
 */
@Configuration
@Slf4j
public class FeignConfiguration implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        if (requestTemplate.feignTarget().name().equalsIgnoreCase("ai-maxkb-service")) {
            return;
        }
        // 注意熔断器的模式 如果是线程池模式 不同线程 UserThreadContext.getJwt() 取不到值
        // 如果是信号量模式可以取到线程数据
        requestTemplate.header(SecureConstant.BASIC_HEADER_KEY, UserThreadContext.getJwt());
        requestTemplate.header(SecureConstant.EXPORT_HEADER_KEY, UserThreadContext.getIsExport());
        requestTemplate.header(SecureConstant.ACCEPT_LANGUAGE_KEY, UserThreadContext.getAcceptLanguage());
        requestTemplate.header(
            TokenConstant.HEADER_FEIGN_SOURCE_KEY, TokenConstant.HEADER_FEIGN_SOURCE_VALUE);
        requestTemplate.header(SecureConstant.X_FORWARDED_FOR, UserThreadContext.getXForwardedFor());
        requestTemplate.header(TokenConstant.TENANT_ID, UserThreadContext.getTenantId());
        HttpServletRequest request = WebUtil.getRequest();
        Optional.ofNullable(request)
            .ifPresent(r -> requestTemplate.header(TokenConstant.HOST, r.getServerName()));
        String traceId = MDC.get("traceId");
        if (StringUtils.isNotBlank(traceId)) {
            requestTemplate.header(TokenConstant.X_B3_TRACE_ID, traceId);
        }
        log.info("FeignConfiguration UserThreadContext.getTenantId():{}", UserThreadContext.getTenantId());

        // 分布式事务必须带xid到下一个服务里
    }

    @Bean
    public ErrorDecoder errorDecoder() {
        return (methodKey, response) -> {
            Exception exception = null;
            try {

                String json = Util.toString(response.body().asReader(Charset.defaultCharset()));

                log.error("response.status:{}, feign error info: {}", response.status(), json);

                // 获取原始返回内容
                exception = new RuntimeException(json);
                // 将返回内容反序列化为Result，这里应根据自身项目作修改
                Result<?> result = JsonUtil.jsonToObj(json, Result.class);
                if (response.status() == 500 && (result == null || result.getCode() == null)) {
                    return new BusinessException(ErrorNoEnum.ERR_SERVER);
                }
                // 业务异常抛出简单的 RuntimeException，保留原来错误信息
                if (result != null && !Objects.equals(result.getCode(), 0)) {
                    exception = new BusinessException(result.getCode(), result.getMessage(), result.getData(),
                        result.getArgs());
                }
            } catch (IOException ex) {
                log.error(ex.getMessage(), ex);
            }
            return exception;
        };
    }
}
