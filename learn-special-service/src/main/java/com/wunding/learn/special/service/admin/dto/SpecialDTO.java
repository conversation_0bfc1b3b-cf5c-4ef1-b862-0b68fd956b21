package com.wunding.learn.special.service.admin.dto;

import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.flowable.api.dto.BaseAuditStatusDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitBaseInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 专题查看对象
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(name = "SpecialDTO", description = "专题查看对象")
public class SpecialDTO extends BaseAuditStatusDTO {

    @Schema(description = "专题ID")
    private String id;

    @Schema(description = "专题名称")
    private String specialName;

    @Schema(description = "专题分类Id")
    private String specialClassId;

    @Schema(description = "专题分类名称")
    private String specialClassName;

    @Schema(description = "专题简介")
    private String specialDesc;

    @Schema(description = "封面地址")
    private String coverImagePath;

    @Schema(description = "封面图片名称")
    private String coverImageName;

    @Schema(description = "封面地址url")
    private String coverImageUrl;

    @Schema(description = "计划类型 0固定周期，1固定日期")
    private Integer type;

    @Schema(description = "固定周期天数")
    private Integer cycleDay;

    @Schema(description = "固定日期开始时间")
    private Date startTime;

    @Schema(description = "固定日期结束时间")
    private Date endTime;

    @Schema(description = "班主任用户id")
    private String leaderId;

    @Schema(description = "班主任名称")
    private String leaderName;

    @Schema(description = "备注")
    private String mark;

    @Schema(description = "标签Id")
    private String label;

    @Schema(description = "标签名称")
    private String labelName;

    @Schema(description = "专题阶段")
    private List<ProphaseDTO> specialPhaseSave;

    @Schema(description = "专题栏目 开通应用")
    private List<String> projectItem;

    @Schema(description = "背景图片路径")
    private String backImagePath;

    @Schema(description = "背景图片名称")
    private String backImageName;

    @Schema(description = "背景图片Url")
    private String backImageUrl;

    @Schema(description = " 背景显示方式 1 横向平铺， 0 不平铺")
    private Integer backType;

    @Schema(description = "是否发布")
    private Integer isPublish;

    @Data
    @Schema(name = "ProphaseDTO", description = "专题阶段对象")
    public static class ProphaseDTO {

        @Schema(description = "专题阶段id")
        private String id;

        @Schema(description = "专题阶段名称")
        private String name;

        @Schema(description = "专题阶段排序")
        private Integer sort;

        @Schema(description = "描述说明")
        private String remark;

        @Schema(description = "专题阶段图片")
        private List<NamePath> phaseImgList;
    }

    @Schema(description = "下发方式：0 部分可见 1仅创建者可见 2所有人可见", required = true)
    @NotNull(message = "下发可见方式不可为空")
    private Integer viewType;

    @Schema(description = "下发范围基本信息")
    private ViewLimitBaseInfoDTO limit;

    @Schema(description = "创建、归属组织Id")
    private String orgId;

    @Schema(description = "创建、归属组织")
    private String orgName;

    @Schema(description = "创建者id")
    private String createBy;

    @Schema(description = "分类的路径")
    private String cateLevelPath;
}
